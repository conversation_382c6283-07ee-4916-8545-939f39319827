<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">

    <!-- Arabic Font Configuration -->
    <FontFamily x:Key="ArabicFont">Segoe UI, Tahoma, Arial Unicode MS, Microsoft Sans Serif</FontFamily>
    
    <!-- Enhanced Arabic Text Styles with English Numbers -->
    <Style x:Key="ArabicTextStyle" TargetType="{x:Type TextBlock}">
        <Setter Property="FontFamily" Value="{StaticResource ArabicFont}"/>
        <Setter Property="FlowDirection" Value="RightToLeft"/>
        <Setter Property="TextOptions.TextFormattingMode" Value="Display"/>
        <Setter Property="TextOptions.TextRenderingMode" Value="ClearType"/>
        <Setter Property="UseLayoutRounding" Value="True"/>
        <Setter Property="SnapsToDevicePixels" Value="True"/>
        <Setter Property="TextOptions.TextHintingMode" Value="Fixed"/>
        <Setter Property="Language" Value="en-US"/>
    </Style>

    <Style x:Key="ArabicInputStyle" TargetType="{x:Type TextBox}">
        <Setter Property="FontFamily" Value="{StaticResource ArabicFont}"/>
        <Setter Property="FlowDirection" Value="RightToLeft"/>
        <Setter Property="TextOptions.TextFormattingMode" Value="Display"/>
        <Setter Property="TextOptions.TextRenderingMode" Value="ClearType"/>
        <Setter Property="UseLayoutRounding" Value="True"/>
        <Setter Property="SnapsToDevicePixels" Value="True"/>
        <Setter Property="TextOptions.TextHintingMode" Value="Fixed"/>
    </Style>

    <Style x:Key="ArabicLabelStyle" TargetType="{x:Type Label}">
        <Setter Property="FontFamily" Value="{StaticResource ArabicFont}"/>
        <Setter Property="FlowDirection" Value="RightToLeft"/>
        <Setter Property="TextOptions.TextFormattingMode" Value="Display"/>
        <Setter Property="TextOptions.TextRenderingMode" Value="ClearType"/>
        <Setter Property="UseLayoutRounding" Value="True"/>
        <Setter Property="SnapsToDevicePixels" Value="True"/>
        <Setter Property="TextOptions.TextHintingMode" Value="Fixed"/>
    </Style>

    <Style x:Key="ArabicButtonStyle" TargetType="{x:Type Button}">
        <Setter Property="FontFamily" Value="{StaticResource ArabicFont}"/>
        <Setter Property="FlowDirection" Value="RightToLeft"/>
        <Setter Property="TextOptions.TextFormattingMode" Value="Display"/>
        <Setter Property="TextOptions.TextRenderingMode" Value="ClearType"/>
        <Setter Property="UseLayoutRounding" Value="True"/>
        <Setter Property="SnapsToDevicePixels" Value="True"/>
        <Setter Property="TextOptions.TextHintingMode" Value="Fixed"/>
    </Style>

    <!-- DataGrid Arabic Styles with English Numbers -->
    <Style x:Key="ArabicDataGridStyle" TargetType="{x:Type DataGrid}">
        <Setter Property="FontFamily" Value="{StaticResource ArabicFont}"/>
        <Setter Property="FlowDirection" Value="RightToLeft"/>
        <Setter Property="TextOptions.TextFormattingMode" Value="Display"/>
        <Setter Property="TextOptions.TextRenderingMode" Value="ClearType"/>
        <Setter Property="UseLayoutRounding" Value="True"/>
        <Setter Property="SnapsToDevicePixels" Value="True"/>
        <Setter Property="Language" Value="en-US"/>
    </Style>

    <Style x:Key="ArabicDataGridCellStyle" TargetType="{x:Type DataGridCell}">
        <Setter Property="FontFamily" Value="{StaticResource ArabicFont}"/>
        <Setter Property="TextOptions.TextFormattingMode" Value="Display"/>
        <Setter Property="TextOptions.TextRenderingMode" Value="ClearType"/>
        <Setter Property="UseLayoutRounding" Value="True"/>
        <Setter Property="SnapsToDevicePixels" Value="True"/>
        <Setter Property="Language" Value="en-US"/>
    </Style>

    <Style x:Key="ArabicDataGridHeaderStyle" TargetType="{x:Type DataGridColumnHeader}">
        <Setter Property="FontFamily" Value="{StaticResource ArabicFont}"/>
        <Setter Property="TextOptions.TextFormattingMode" Value="Display"/>
        <Setter Property="TextOptions.TextRenderingMode" Value="ClearType"/>
        <Setter Property="UseLayoutRounding" Value="True"/>
        <Setter Property="SnapsToDevicePixels" Value="True"/>
        <Setter Property="Language" Value="en-US"/>
    </Style>

    <!-- Special style for numeric TextBlocks -->
    <Style x:Key="NumericTextStyle" TargetType="{x:Type TextBlock}">
        <Setter Property="FontFamily" Value="Segoe UI, Consolas, Arial"/>
        <Setter Property="Language" Value="en-US"/>
        <Setter Property="FlowDirection" Value="LeftToRight"/>
        <Setter Property="TextAlignment" Value="Center"/>
        <Setter Property="FontWeight" Value="SemiBold"/>
    </Style>

    <!-- ComboBox Arabic Style -->
    <Style x:Key="ArabicComboBoxStyle" TargetType="{x:Type ComboBox}">
        <Setter Property="FontFamily" Value="{StaticResource ArabicFont}"/>
        <Setter Property="FlowDirection" Value="RightToLeft"/>
        <Setter Property="TextOptions.TextFormattingMode" Value="Display"/>
        <Setter Property="TextOptions.TextRenderingMode" Value="ClearType"/>
        <Setter Property="UseLayoutRounding" Value="True"/>
        <Setter Property="SnapsToDevicePixels" Value="True"/>
    </Style>

    <!-- ListBox Arabic Style -->
    <Style x:Key="ArabicListBoxStyle" TargetType="{x:Type ListBox}">
        <Setter Property="FontFamily" Value="{StaticResource ArabicFont}"/>
        <Setter Property="FlowDirection" Value="RightToLeft"/>
        <Setter Property="TextOptions.TextFormattingMode" Value="Display"/>
        <Setter Property="TextOptions.TextRenderingMode" Value="ClearType"/>
        <Setter Property="UseLayoutRounding" Value="True"/>
        <Setter Property="SnapsToDevicePixels" Value="True"/>
    </Style>

</ResourceDictionary>
