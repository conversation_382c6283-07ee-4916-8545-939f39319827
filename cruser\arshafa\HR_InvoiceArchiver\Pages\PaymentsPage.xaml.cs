using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Threading;
using Microsoft.Extensions.DependencyInjection;
using HR_InvoiceArchiver.Models;
using HR_InvoiceArchiver.Services;

namespace HR_InvoiceArchiver.Pages
{
    public partial class PaymentsPage : UserControl, INavigationAware
    {
        private readonly IPaymentService _paymentService;
        private readonly IToastService _toastService;
        private readonly INavigationService _navigationService;
        private ObservableCollection<Payment> _allPayments;
        private ObservableCollection<Payment> _filteredPayments;

        public PaymentsPage(
            IPaymentService paymentService,
            IToastService toastService,
            INavigationService navigationService)
        {
            try
            {
                System.Console.WriteLine("PaymentsPage: Constructor started");
                InitializeComponent();

                _paymentService = paymentService ?? throw new ArgumentNullException(nameof(paymentService));
                _toastService = toastService ?? throw new ArgumentNullException(nameof(toastService));
                _navigationService = navigationService ?? throw new ArgumentNullException(nameof(navigationService));

                _allPayments = new ObservableCollection<Payment>();
                _filteredPayments = new ObservableCollection<Payment>();

                PaymentsDataGrid.ItemsSource = _filteredPayments;

                System.Console.WriteLine("PaymentsPage: Constructor completed successfully");
            }
            catch (Exception ex)
            {
                System.Console.WriteLine($"PaymentsPage: Constructor error: {ex.Message}");
                System.Console.WriteLine($"PaymentsPage: Stack trace: {ex.StackTrace}");
                throw;
            }
        }



        public async void OnNavigatedTo(object parameter)
        {
            System.Console.WriteLine("PaymentsPage: OnNavigatedTo called");
            try
            {
                // Handle navigation parameters
                if (parameter is string action && action == "add")
                {
                    System.Console.WriteLine("PaymentsPage: Add payment parameter detected");
                    AddPaymentButton_Click(this, new RoutedEventArgs());
                }
                else if (parameter is string multiAction && multiAction == "multi")
                {
                    System.Console.WriteLine("PaymentsPage: Multi payment parameter detected");
                    AddMultiPaymentButton_Click(this, new RoutedEventArgs());
                }
                else
                {
                    System.Console.WriteLine("PaymentsPage: Loading payments data");
                    await LoadPaymentsAsync();
                }
            }
            catch (Exception ex)
            {
                System.Console.WriteLine($"PaymentsPage: OnNavigatedTo error: {ex.Message}");
                _toastService?.ShowError("خطأ", $"حدث خطأ أثناء التنقل: {ex.Message}");
            }
        }

        public void OnNavigatedFrom()
        {
            System.Console.WriteLine("PaymentsPage: OnNavigatedFrom called");
        }

        private async Task LoadPaymentsAsync()
        {
            try
            {
                System.Console.WriteLine("PaymentsPage: LoadPaymentsAsync started");

                // Show loading
                LoadingPanel.Visibility = Visibility.Visible;
                EmptyStatePanel.Visibility = Visibility.Collapsed;

                // Load payments
                var payments = await _paymentService.GetAllPaymentsAsync();

                _allPayments.Clear();
                _filteredPayments.Clear();

                foreach (var payment in payments)
                {
                    _allPayments.Add(payment);
                    _filteredPayments.Add(payment);
                }

                // Update statistics
                await UpdateStatisticsAsync();

                // Hide loading and show content
                LoadingPanel.Visibility = Visibility.Collapsed;

                if (_filteredPayments.Count == 0)
                {
                    EmptyStatePanel.Visibility = Visibility.Visible;
                }
                else
                {
                    EmptyStatePanel.Visibility = Visibility.Collapsed;
                }

                // Update last update time
                LastUpdateText.Text = DateTime.Now.ToString("HH:mm:ss");

                System.Console.WriteLine("PaymentsPage: LoadPaymentsAsync completed successfully");
                _toastService?.ShowSuccess("تم التحديث", "تم تحميل المدفوعات بنجاح");
            }
            catch (Exception ex)
            {
                System.Console.WriteLine($"PaymentsPage: LoadPaymentsAsync error: {ex.Message}");
                LoadingPanel.Visibility = Visibility.Collapsed;
                _toastService?.ShowError("خطأ", $"حدث خطأ أثناء تحميل المدفوعات: {ex.Message}");
            }
        }

        private async Task UpdateStatisticsAsync()
        {
            try
            {
                var statistics = await _paymentService.GetPaymentStatisticsAsync();

                // Update header statistics only
                HeaderTotalPaymentsText.Text = statistics.TotalPayments.ToString("N0");
                HeaderTotalAmountText.Text = $"{statistics.TotalAmount:N0}";

                // Update payment count
                PaymentCountText.Text = $"{_filteredPayments.Count} دفعة";

                System.Console.WriteLine("PaymentsPage: Statistics updated successfully");
            }
            catch (Exception ex)
            {
                System.Console.WriteLine($"PaymentsPage: UpdateStatisticsAsync error: {ex.Message}");
            }
        }

        private void FilterPayments()
        {
            // استخدام النظام الجديد للتصفية المتقدمة
            ApplyFiltersAsync();
        }

        // Event Handlers
        private async void AddPaymentButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var addEditPaymentWindow = App.ServiceProvider.GetService(typeof(Windows.AddEditPaymentWindow)) as Windows.AddEditPaymentWindow;
                if (addEditPaymentWindow != null)
                {
                    addEditPaymentWindow.Owner = Window.GetWindow(this);
                    addEditPaymentWindow.ShowDialog();

                    // Refresh the payments list after closing
                    await LoadPaymentsAsync();
                }
            }
            catch (Exception ex)
            {
                _toastService?.ShowError("خطأ", $"حدث خطأ أثناء فتح نافذة إضافة الدفعة: {ex.Message}");
            }
        }

        private void AddMultiPaymentButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                ShowMultiPaymentForm();
            }
            catch (Exception ex)
            {
                _toastService?.ShowError("خطأ", $"حدث خطأ أثناء فتح نافذة الدفع المتعدد: {ex.Message}");
            }
        }

        private void ShowMultiPaymentForm()
        {
            // Clear existing content
            MultiPaymentContainer.Children.Clear();

            // Create new MultiPaymentFormControl
            var multiPaymentForm = new Controls.MultiPaymentFormControl();
            multiPaymentForm.FormClosed += MultiPaymentForm_FormClosed;

            // Add to container
            MultiPaymentContainer.Children.Add(multiPaymentForm);

            // Show overlay with animation
            MultiPaymentOverlay.Visibility = Visibility.Visible;

            // Fade in animation
            var fadeIn = new System.Windows.Media.Animation.DoubleAnimation
            {
                From = 0,
                To = 1,
                Duration = TimeSpan.FromMilliseconds(300)
            };
            MultiPaymentOverlay.BeginAnimation(UIElement.OpacityProperty, fadeIn);
        }

        private void MultiPaymentForm_FormClosed(object? sender, Controls.MultiPaymentEventArgs e)
        {
            // Hide overlay with animation
            var fadeOut = new System.Windows.Media.Animation.DoubleAnimation
            {
                From = 1,
                To = 0,
                Duration = TimeSpan.FromMilliseconds(300)
            };

            fadeOut.Completed += (s, args) =>
            {
                MultiPaymentOverlay.Visibility = Visibility.Collapsed;
                MultiPaymentContainer.Children.Clear();
            };

            MultiPaymentOverlay.BeginAnimation(UIElement.OpacityProperty, fadeOut);

            // Refresh payments list if payment was successful
            if (e.Success)
            {
                _ = LoadPaymentsAsync(); // Fire and forget
            }
        }

        private async void RefreshButton_Click(object sender, RoutedEventArgs e)
        {
            await LoadPaymentsAsync();
        }

        private void SearchTextBox_TextChanged(object sender, TextChangedEventArgs e)
        {
            FilterPayments();
        }

        private void ClearSearchButton_Click(object sender, RoutedEventArgs e)
        {
            SearchTextBox.Text = "";
            _toastService?.ShowInfo("تم المسح", "تم مسح البحث");
        }

        private void ToggleFilters_Click(object sender, RoutedEventArgs e)
        {
            if (FiltersPanel.Visibility == Visibility.Collapsed)
            {
                FiltersPanel.Visibility = Visibility.Visible;
                ToggleFiltersButton.Content = "إخفاء التصفية";
            }
            else
            {
                FiltersPanel.Visibility = Visibility.Collapsed;
                ToggleFiltersButton.Content = "تصفية متقدمة";
            }
        }

        private void DateFilter_Changed(object sender, SelectionChangedEventArgs e)
        {
            if (IsLoaded)
                ApplyFiltersAsync();
        }

        private void AmountFilter_Changed(object sender, TextChangedEventArgs e)
        {
            if (IsLoaded)
                ApplyFiltersAsync();
        }

        private void PaymentMethodFilter_Changed(object sender, SelectionChangedEventArgs e)
        {
            if (IsLoaded)
                ApplyFiltersAsync();
        }

        private void PaymentStatusFilter_Changed(object sender, SelectionChangedEventArgs e)
        {
            if (IsLoaded)
                ApplyFiltersAsync();
        }

        private void ApplyFilters_Click(object sender, RoutedEventArgs e)
        {
            ApplyFiltersAsync();
        }

        private void ClearFilters_Click(object sender, RoutedEventArgs e)
        {
            // مسح جميع المرشحات
            FromDatePicker.SelectedDate = null;
            ToDatePicker.SelectedDate = null;
            MinAmountTextBox.Text = string.Empty;
            MaxAmountTextBox.Text = string.Empty;
            PaymentMethodComboBox.SelectedIndex = 0;
            PaymentStatusComboBox.SelectedIndex = 0;

            // تطبيق التصفية الفارغة
            ApplyFiltersAsync();

            _toastService?.ShowInfo("تم المسح", "تم مسح جميع المرشحات");
        }

        private async void ApplyFiltersAsync()
        {
            try
            {
                var allPayments = await _paymentService.GetAllPaymentsAsync();
                var filteredPayments = allPayments.AsEnumerable();

                // تطبيق تصفية النص
                if (!string.IsNullOrWhiteSpace(SearchTextBox.Text))
                {
                    var searchText = SearchTextBox.Text.ToLower();
                    filteredPayments = filteredPayments.Where(p =>
                        p.ReceiptNumber.ToLower().Contains(searchText) ||
                        (p.SupplierName?.ToLower().Contains(searchText) ?? false) ||
                        (p.Notes?.ToLower().Contains(searchText) ?? false));
                }

                // تصفية التاريخ
                if (FromDatePicker.SelectedDate.HasValue)
                {
                    filteredPayments = filteredPayments.Where(p => p.PaymentDate >= FromDatePicker.SelectedDate.Value);
                }

                if (ToDatePicker.SelectedDate.HasValue)
                {
                    filteredPayments = filteredPayments.Where(p => p.PaymentDate <= ToDatePicker.SelectedDate.Value);
                }

                // تصفية المبلغ
                if (decimal.TryParse(MinAmountTextBox.Text, out decimal minAmount))
                {
                    filteredPayments = filteredPayments.Where(p => p.Amount >= minAmount);
                }

                if (decimal.TryParse(MaxAmountTextBox.Text, out decimal maxAmount))
                {
                    filteredPayments = filteredPayments.Where(p => p.Amount <= maxAmount);
                }

                // تصفية طريقة الدفع
                if (PaymentMethodComboBox.SelectedItem is ComboBoxItem methodItem &&
                    methodItem.Tag?.ToString() != "All")
                {
                    var tagValue = methodItem.Tag?.ToString();
                    if (!string.IsNullOrEmpty(tagValue) && Enum.TryParse<PaymentMethod>(tagValue, out var method))
                    {
                        filteredPayments = filteredPayments.Where(p => p.Method == method);
                    }
                }

                // تصفية حالة الدفع
                if (PaymentStatusComboBox.SelectedItem is ComboBoxItem statusItem &&
                    statusItem.Tag?.ToString() != "All")
                {
                    var tagValue = statusItem.Tag?.ToString();
                    if (!string.IsNullOrEmpty(tagValue) && Enum.TryParse<PaymentStatus>(tagValue, out var status))
                    {
                        filteredPayments = filteredPayments.Where(p => p.Status == status);
                    }
                }

                // تحديث الجدول
                PaymentsDataGrid.ItemsSource = filteredPayments.ToList();

                // تحديث الإحصائيات
                await UpdateStatisticsAsync();
            }
            catch (Exception ex)
            {
                _toastService?.ShowError("خطأ في التصفية", $"فشل في تطبيق المرشحات: {ex.Message}");
            }
        }

        private void AdvancedSearchButton_Click(object sender, RoutedEventArgs e)
        {
            _toastService?.ShowInfo("البحث المتقدم", "سيتم إضافة البحث المتقدم قريباً");
        }



        // Payment Actions Event Handlers
        private void ViewReceiptAttachment_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (sender is Button button && button.Tag is Payment payment)
                {
                    if (string.IsNullOrEmpty(payment.AttachmentPath))
                    {
                        _toastService?.ShowWarning("تنبيه", "لا يوجد مرفق لهذا الوصل");
                        return;
                    }

                    OpenPaymentAttachment(payment);
                }
            }
            catch (Exception ex)
            {
                _toastService?.ShowError("خطأ", $"فشل في عرض مرفق الوصل: {ex.Message}");
            }
        }

        private void ViewPaymentDetails_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (sender is Button button && button.Tag is Payment payment)
                {
                    ShowPaymentDetailsOverlay(payment);
                }
            }
            catch (Exception ex)
            {
                _toastService?.ShowError("خطأ", $"فشل في عرض تفاصيل الدفعة: {ex.Message}");
            }
        }



        private async void EditPayment_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (sender is Button button && button.Tag is Payment payment)
                {
                    // Create new instance with payment data for editing
                    var addEditPaymentWindow = new Windows.AddEditPaymentWindow(payment);
                    addEditPaymentWindow.Owner = Window.GetWindow(this);

                    var result = addEditPaymentWindow.ShowDialog();

                    // Refresh the payments list if changes were saved
                    if (result == true)
                    {
                        await LoadPaymentsAsync();
                    }
                }
            }
            catch (Exception ex)
            {
                _toastService?.ShowError("خطأ", $"فشل في فتح نافذة تعديل الدفعة: {ex.Message}");
            }
        }

        private async void DeletePayment_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (sender is Button button && button.Tag is Payment payment)
                {
                    // استخدام نافذة التأكيد الجديدة
                    var parentWindow = Window.GetWindow(this);
                    bool confirmed = Controls.ConfirmationDialog.ShowPaymentDeletionConfirmation(parentWindow, payment);

                    if (confirmed)
                    {
                        // إظهار مؤشر التحميل
                        _toastService?.ShowInfo("جاري الحذف", "جاري حذف المدفوعة...");

                        await _paymentService.DeletePaymentAsync(payment.Id);

                        _toastService?.ShowSuccess("تم الحذف بنجاح",
                            $"تم حذف الوصل رقم {payment.ReceiptNumber} وجميع المرفقات المرتبطة به");

                        await LoadPaymentsAsync();
                    }
                }
            }
            catch (Exception ex)
            {
                _toastService?.ShowError("خطأ في الحذف", $"فشل في حذف الدفعة: {ex.Message}");
            }
        }

        // Helper Methods
        private void OpenPaymentAttachment(Payment payment)
        {
            try
            {
                if (string.IsNullOrEmpty(payment.AttachmentPath))
                {
                    _toastService?.ShowWarning("تنبيه", "لا يوجد مرفق لهذا الوصل");
                    return;
                }

                Utils.FileHelper.OpenAttachment(payment.AttachmentPath, "Payments");
                _toastService?.ShowSuccess("تم فتح المرفق", $"تم فتح مرفق الوصل رقم {payment.ReceiptNumber}");
            }
            catch (FileNotFoundException)
            {
                _toastService?.ShowError("ملف غير موجود", "مرفق الوصل غير موجود في المسار المحدد");
            }
            catch (Exception ex)
            {
                _toastService?.ShowError("خطأ في فتح الملف", $"فشل في فتح مرفق الوصل: {ex.Message}");
            }
        }



        private void ShowPaymentDetailsOverlay(Payment payment)
        {
            try
            {
                // Clear existing content
                PaymentDetailsContainer.Children.Clear();

                // Create new PaymentDetailsControl
                var paymentDetailsControl = new Controls.PaymentDetailsControl();
                paymentDetailsControl.FormClosed += PaymentDetailsControl_FormClosed;
                paymentDetailsControl.LoadPaymentDetails(payment);

                // Add to container
                PaymentDetailsContainer.Children.Add(paymentDetailsControl);

                // Show overlay with animation
                PaymentDetailsOverlay.Visibility = Visibility.Visible;

                // Fade in animation
                var fadeIn = new System.Windows.Media.Animation.DoubleAnimation
                {
                    From = 0,
                    To = 1,
                    Duration = TimeSpan.FromMilliseconds(300)
                };
                PaymentDetailsOverlay.BeginAnimation(UIElement.OpacityProperty, fadeIn);
            }
            catch (Exception ex)
            {
                _toastService?.ShowError("خطأ", $"فشل في عرض تفاصيل الدفعة: {ex.Message}");
            }
        }

        private void PaymentDetailsControl_FormClosed(object? sender, EventArgs e)
        {
            // Hide overlay with animation
            var fadeOut = new System.Windows.Media.Animation.DoubleAnimation
            {
                From = 1,
                To = 0,
                Duration = TimeSpan.FromMilliseconds(300)
            };

            fadeOut.Completed += (s, args) =>
            {
                PaymentDetailsOverlay.Visibility = Visibility.Collapsed;
                PaymentDetailsContainer.Children.Clear();
            };

            PaymentDetailsOverlay.BeginAnimation(UIElement.OpacityProperty, fadeOut);
        }

        private void ShowPaymentDetailsDialog_Old(Payment payment)
        {
            try
            {
                var detailsWindow = new Window
                {
                    Title = $"تفاصيل الدفعة - {payment.ReceiptNumber}",
                    Width = 600,
                    Height = 700,
                    WindowStartupLocation = WindowStartupLocation.CenterOwner,
                    Owner = Window.GetWindow(this),
                    ResizeMode = ResizeMode.CanResize,
                    WindowStyle = WindowStyle.SingleBorderWindow,
                    Background = new SolidColorBrush(Color.FromRgb(248, 249, 250))
                };

                var mainGrid = new Grid();
                mainGrid.RowDefinitions.Add(new RowDefinition { Height = GridLength.Auto }); // Header
                mainGrid.RowDefinitions.Add(new RowDefinition { Height = new GridLength(1, GridUnitType.Star) }); // Content
                mainGrid.RowDefinitions.Add(new RowDefinition { Height = GridLength.Auto }); // Footer

                // Header Section
                var headerBorder = new Border
                {
                    Background = new LinearGradientBrush(
                        Color.FromRgb(0, 123, 255),
                        Color.FromRgb(0, 86, 179),
                        new Point(0, 0),
                        new Point(1, 1)),
                    Padding = new Thickness(25, 20, 25, 20),
                    CornerRadius = new CornerRadius(0, 0, 0, 0)
                };

                var headerPanel = new StackPanel
                {
                    Orientation = Orientation.Horizontal
                };

                var headerIcon = new TextBlock
                {
                    Text = "💳",
                    FontSize = 32,
                    Margin = new Thickness(0, 0, 15, 0),
                    VerticalAlignment = VerticalAlignment.Center
                };

                var headerTextPanel = new StackPanel();
                var headerTitle = new TextBlock
                {
                    Text = $"وصل دفع رقم: {payment.ReceiptNumber}",
                    FontSize = 20,
                    FontWeight = FontWeights.Bold,
                    Foreground = Brushes.White
                };

                var headerSubtitle = new TextBlock
                {
                    Text = $"المورد: {payment.SupplierName}",
                    FontSize = 14,
                    Foreground = new SolidColorBrush(Color.FromRgb(220, 220, 220)),
                    Margin = new Thickness(0, 5, 0, 0)
                };

                headerTextPanel.Children.Add(headerTitle);
                headerTextPanel.Children.Add(headerSubtitle);
                headerPanel.Children.Add(headerIcon);
                headerPanel.Children.Add(headerTextPanel);
                headerBorder.Child = headerPanel;

                // Content Section
                var contentScrollViewer = new ScrollViewer
                {
                    VerticalScrollBarVisibility = ScrollBarVisibility.Auto,
                    Padding = new Thickness(25, 20, 25, 20)
                };

                var contentPanel = new StackPanel();

                // Payment Information Card
                var paymentCard = CreateInfoCard("معلومات الدفعة", new[]
                {
                    new { Icon = "💰", Label = "المبلغ المدفوع", Value = $"{payment.Amount:N0} د.ع", Color = "#28A745" },
                    new { Icon = "📅", Label = "تاريخ الدفع", Value = payment.PaymentDate.ToString("dd/MM/yyyy"), Color = "#007BFF" },
                    new { Icon = "💳", Label = "طريقة الدفع", Value = payment.PaymentMethodText, Color = "#6F42C1" },
                    new { Icon = "📊", Label = "حالة التسديد", Value = payment.StatusText, Color = "#17A2B8" }
                });

                contentPanel.Children.Add(paymentCard);

                // Financial Details Card
                if (payment.DiscountAmount > 0 || payment.RefundValue > 0)
                {
                    var financialCard = CreateInfoCard("التفاصيل المالية", new[]
                    {
                        new { Icon = "💸", Label = "مبلغ الخصم", Value = $"{payment.DiscountAmount:N0} د.ع", Color = "#FFC107" },
                        new { Icon = "↩️", Label = "قيمة الاسترجاع", Value = $"{payment.RefundValue:N0} د.ع", Color = "#DC3545" },
                        new { Icon = "🧮", Label = "إجمالي التسوية", Value = $"{payment.TotalSettlement:N0} د.ع", Color = "#28A745" }
                    });

                    contentPanel.Children.Add(financialCard);
                }

                // Invoice Information Card
                var invoiceCard = CreateInfoCard("معلومات الفاتورة", new[]
                {
                    new { Icon = "📄", Label = "رقم الفاتورة", Value = payment.InvoiceNumber, Color = "#007BFF" },
                    new { Icon = "💼", Label = "اسم المورد", Value = payment.SupplierName, Color = "#6C757D" },
                    new { Icon = "💰", Label = "المبلغ المتبقي", Value = $"{payment.Invoice?.RemainingAmount:N0} د.ع", Color = "#FD7E14" }
                });

                contentPanel.Children.Add(invoiceCard);

                // Notes Card
                if (!string.IsNullOrEmpty(payment.Notes))
                {
                    var notesCard = CreateInfoCard("ملاحظات", new[]
                    {
                        new { Icon = "📝", Label = "التفاصيل", Value = payment.Notes, Color = "#6C757D" }
                    });

                    contentPanel.Children.Add(notesCard);
                }

                // System Information Card
                var systemCard = CreateInfoCard("معلومات النظام", new[]
                {
                    new { Icon = "🕒", Label = "تاريخ الإنشاء", Value = payment.CreatedDate.ToString("dd/MM/yyyy HH:mm"), Color = "#6C757D" },
                    new { Icon = "✏️", Label = "آخر تحديث", Value = payment.UpdatedDate?.ToString("dd/MM/yyyy HH:mm") ?? "لم يتم التحديث", Color = "#6C757D" }
                });

                contentPanel.Children.Add(systemCard);

                contentScrollViewer.Content = contentPanel;

                // Footer Section
                var footerBorder = new Border
                {
                    Background = new SolidColorBrush(Color.FromRgb(248, 249, 250)),
                    BorderBrush = new SolidColorBrush(Color.FromRgb(222, 226, 230)),
                    BorderThickness = new Thickness(0, 1, 0, 0),
                    Padding = new Thickness(25, 15, 25, 15)
                };

                var footerPanel = new StackPanel
                {
                    Orientation = Orientation.Horizontal,
                    HorizontalAlignment = HorizontalAlignment.Right
                };

                // Attachment Button
                if (!string.IsNullOrEmpty(payment.AttachmentPath))
                {
                    var attachmentButton = new Button
                    {
                        Content = "📎 عرض المرفق",
                        Padding = new Thickness(20, 10, 20, 10),
                        Margin = new Thickness(0, 0, 10, 0),
                        Background = new SolidColorBrush(Color.FromRgb(255, 107, 53)),
                        Foreground = Brushes.White,
                        BorderThickness = new Thickness(0),
                        FontWeight = FontWeights.SemiBold
                    };

                    attachmentButton.Click += (s, e) =>
                    {
                        OpenPaymentAttachment(payment);
                    };

                    footerPanel.Children.Add(attachmentButton);
                }

                // Close Button
                var closeButton = new Button
                {
                    Content = "إغلاق",
                    Padding = new Thickness(20, 10, 20, 10),
                    Background = new SolidColorBrush(Color.FromRgb(108, 117, 125)),
                    Foreground = Brushes.White,
                    BorderThickness = new Thickness(0),
                    FontWeight = FontWeights.SemiBold
                };

                closeButton.Click += (s, e) => detailsWindow.Close();
                footerPanel.Children.Add(closeButton);
                footerBorder.Child = footerPanel;

                // Add to main grid
                Grid.SetRow(headerBorder, 0);
                Grid.SetRow(contentScrollViewer, 1);
                Grid.SetRow(footerBorder, 2);

                mainGrid.Children.Add(headerBorder);
                mainGrid.Children.Add(contentScrollViewer);
                mainGrid.Children.Add(footerBorder);

                detailsWindow.Content = mainGrid;
                detailsWindow.ShowDialog();
            }
            catch (Exception ex)
            {
                _toastService?.ShowError("خطأ", $"فشل في عرض تفاصيل الدفعة: {ex.Message}");
            }
        }

        private Border CreateInfoCard(string title, dynamic[] items)
        {
            var card = new Border
            {
                Background = Brushes.White,
                BorderBrush = new SolidColorBrush(Color.FromRgb(222, 226, 230)),
                BorderThickness = new Thickness(1),
                CornerRadius = new CornerRadius(8),
                Margin = new Thickness(0, 0, 0, 20),
                Padding = new Thickness(0)
            };

            var cardPanel = new StackPanel();

            // Card Header
            var headerBorder = new Border
            {
                Background = new SolidColorBrush(Color.FromRgb(248, 249, 250)),
                BorderBrush = new SolidColorBrush(Color.FromRgb(222, 226, 230)),
                BorderThickness = new Thickness(0, 0, 0, 1),
                Padding = new Thickness(20, 15, 20, 15)
            };

            var headerText = new TextBlock
            {
                Text = title,
                FontSize = 16,
                FontWeight = FontWeights.Bold,
                Foreground = new SolidColorBrush(Color.FromRgb(52, 58, 64))
            };

            headerBorder.Child = headerText;
            cardPanel.Children.Add(headerBorder);

            // Card Content
            var contentPanel = new StackPanel
            {
                Margin = new Thickness(20, 15, 20, 15)
            };

            foreach (var item in items)
            {
                var itemPanel = new StackPanel
                {
                    Orientation = Orientation.Horizontal,
                    Margin = new Thickness(0, 0, 0, 12)
                };

                // Icon
                var iconText = new TextBlock
                {
                    Text = item.Icon,
                    FontSize = 18,
                    Margin = new Thickness(0, 0, 12, 0),
                    VerticalAlignment = VerticalAlignment.Center
                };

                // Label and Value Container
                var textContainer = new StackPanel
                {
                    VerticalAlignment = VerticalAlignment.Center
                };

                var labelText = new TextBlock
                {
                    Text = item.Label,
                    FontSize = 12,
                    FontWeight = FontWeights.SemiBold,
                    Foreground = new SolidColorBrush(Color.FromRgb(108, 117, 125)),
                    Margin = new Thickness(0, 0, 0, 2)
                };

                var valueText = new TextBlock
                {
                    Text = item.Value,
                    FontSize = 14,
                    FontWeight = FontWeights.SemiBold,
                    Foreground = new SolidColorBrush((Color)ColorConverter.ConvertFromString(item.Color)),
                    TextWrapping = TextWrapping.Wrap
                };

                textContainer.Children.Add(labelText);
                textContainer.Children.Add(valueText);

                itemPanel.Children.Add(iconText);
                itemPanel.Children.Add(textContainer);

                contentPanel.Children.Add(itemPanel);
            }

            cardPanel.Children.Add(contentPanel);
            card.Child = cardPanel;

            return card;
        }

        // New Quick Filter Methods
        private void TodayFilter_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var today = DateTime.Today;
                var todayPayments = _allPayments.Where(p => p.PaymentDate.Date == today).ToList();

                _filteredPayments.Clear();
                foreach (var payment in todayPayments)
                {
                    _filteredPayments.Add(payment);
                }

                PaymentCountText.Text = $"{_filteredPayments.Count} دفعة - اليوم";
                _toastService?.ShowInfo("فلترة", $"تم عرض {_filteredPayments.Count} دفعة لليوم");
            }
            catch (Exception ex)
            {
                _toastService?.ShowError("خطأ", $"حدث خطأ في الفلترة: {ex.Message}");
            }
        }

        private void WeekFilter_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var weekStart = DateTime.Today.AddDays(-(int)DateTime.Today.DayOfWeek);
                var weekPayments = _allPayments.Where(p => p.PaymentDate.Date >= weekStart).ToList();

                _filteredPayments.Clear();
                foreach (var payment in weekPayments)
                {
                    _filteredPayments.Add(payment);
                }

                PaymentCountText.Text = $"{_filteredPayments.Count} دفعة - هذا الأسبوع";
                _toastService?.ShowInfo("فلترة", $"تم عرض {_filteredPayments.Count} دفعة لهذا الأسبوع");
            }
            catch (Exception ex)
            {
                _toastService?.ShowError("خطأ", $"حدث خطأ في الفلترة: {ex.Message}");
            }
        }

        private void MonthFilter_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var monthStart = new DateTime(DateTime.Today.Year, DateTime.Today.Month, 1);
                var monthPayments = _allPayments.Where(p => p.PaymentDate.Date >= monthStart).ToList();

                _filteredPayments.Clear();
                foreach (var payment in monthPayments)
                {
                    _filteredPayments.Add(payment);
                }

                PaymentCountText.Text = $"{_filteredPayments.Count} دفعة - هذا الشهر";
                _toastService?.ShowInfo("فلترة", $"تم عرض {_filteredPayments.Count} دفعة لهذا الشهر");
            }
            catch (Exception ex)
            {
                _toastService?.ShowError("خطأ", $"حدث خطأ في الفلترة: {ex.Message}");
            }
        }

        private void CompactViewToggle_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                CompactModeToggle.IsChecked = !CompactModeToggle.IsChecked;
                CompactMode_Changed(CompactModeToggle, new RoutedEventArgs());
            }
            catch (Exception ex)
            {
                _toastService?.ShowError("خطأ", $"حدث خطأ في تبديل العرض: {ex.Message}");
            }
        }

        private void CompactMode_Changed(object sender, RoutedEventArgs e)
        {
            try
            {
                if (CompactModeToggle.IsChecked == true)
                {
                    // Enable compact mode
                    PaymentsDataGrid.RowHeight = 45;
                    PaymentsDataGrid.FontSize = 12;
                    _toastService?.ShowInfo("عرض", "تم تفعيل العرض المضغوط");
                }
                else
                {
                    // Disable compact mode
                    PaymentsDataGrid.RowHeight = 55;
                    PaymentsDataGrid.FontSize = 13;
                    _toastService?.ShowInfo("عرض", "تم إلغاء العرض المضغوط");
                }
            }
            catch (Exception ex)
            {
                _toastService?.ShowError("خطأ", $"حدث خطأ في تغيير العرض: {ex.Message}");
            }
        }

        private async void ExportButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                _toastService?.ShowInfo("تصدير", "جاري تصدير البيانات...");
                // TODO: Implement export functionality
                await Task.Delay(1000); // Simulate export process
                _toastService?.ShowSuccess("تصدير", "تم تصدير البيانات بنجاح!");
            }
            catch (Exception ex)
            {
                _toastService?.ShowError("خطأ", $"حدث خطأ أثناء التصدير: {ex.Message}");
            }
        }

        // Keyboard Shortcuts Handler
        private void PaymentsPage_KeyDown(object sender, KeyEventArgs e)
        {
            try
            {
                if (Keyboard.Modifiers == ModifierKeys.Control)
                {
                    switch (e.Key)
                    {
                        case Key.N: // Ctrl+N - Add New Payment
                            AddPaymentButton_Click(this, new RoutedEventArgs());
                            e.Handled = true;
                            _toastService?.ShowInfo("اختصار", "تم فتح نافذة إضافة مدفوعة جديدة (Ctrl+N)");
                            break;

                        case Key.M: // Ctrl+M - Multi Payment
                            AddMultiPaymentButton_Click(this, new RoutedEventArgs());
                            e.Handled = true;
                            _toastService?.ShowInfo("اختصار", "تم فتح نافذة الوصل المتعدد (Ctrl+M)");
                            break;

                        case Key.F: // Ctrl+F - Focus Search
                            SearchTextBox.Focus();
                            SearchTextBox.SelectAll();
                            e.Handled = true;
                            _toastService?.ShowInfo("اختصار", "تم التركيز على مربع البحث (Ctrl+F)");
                            break;

                        case Key.E: // Ctrl+E - Export
                            ExportButton_Click(this, new RoutedEventArgs());
                            e.Handled = true;
                            break;

                        case Key.R: // Ctrl+R - Refresh
                            RefreshButton_Click(this, new RoutedEventArgs());
                            e.Handled = true;
                            _toastService?.ShowInfo("اختصار", "تم تحديث البيانات (Ctrl+R)");
                            break;

                        case Key.D1: // Ctrl+1 - Today Filter
                            TodayFilter_Click(this, new RoutedEventArgs());
                            e.Handled = true;
                            break;

                        case Key.D2: // Ctrl+2 - Week Filter
                            WeekFilter_Click(this, new RoutedEventArgs());
                            e.Handled = true;
                            break;

                        case Key.D3: // Ctrl+3 - Month Filter
                            MonthFilter_Click(this, new RoutedEventArgs());
                            e.Handled = true;
                            break;
                    }
                }
                else if (e.Key == Key.F5) // F5 - Refresh
                {
                    RefreshButton_Click(this, new RoutedEventArgs());
                    e.Handled = true;
                    _toastService?.ShowInfo("اختصار", "تم تحديث البيانات (F5)");
                }
                else if (e.Key == Key.Escape) // Escape - Clear Search
                {
                    if (!string.IsNullOrEmpty(SearchTextBox.Text))
                    {
                        ClearSearchButton_Click(this, new RoutedEventArgs());
                        e.Handled = true;
                    }
                }
            }
            catch (Exception ex)
            {
                _toastService?.ShowError("خطأ", $"حدث خطأ في معالجة الاختصار: {ex.Message}");
            }
        }

        private void KeyboardShortcuts_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var shortcuts = @"🎹 اختصارات لوحة المفاتيح:

📝 العمليات الأساسية:
• Ctrl+N - إضافة مدفوعة جديدة
• Ctrl+M - إنشاء وصل متعدد
• Ctrl+E - تصدير البيانات
• F5 أو Ctrl+R - تحديث البيانات

🔍 البحث والفلترة:
• Ctrl+F - التركيز على مربع البحث
• Ctrl+1 - عرض مدفوعات اليوم
• Ctrl+2 - عرض مدفوعات الأسبوع
• Ctrl+3 - عرض مدفوعات الشهر
• Escape - مسح البحث

💡 نصائح:
• استخدم Tab للتنقل بين العناصر
• استخدم Enter لتأكيد العمليات
• استخدم Escape للإلغاء أو الخروج";

                MessageBox.Show(shortcuts, "اختصارات لوحة المفاتيح", MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                _toastService?.ShowError("خطأ", $"حدث خطأ في عرض الاختصارات: {ex.Message}");
            }
        }
    }
}
