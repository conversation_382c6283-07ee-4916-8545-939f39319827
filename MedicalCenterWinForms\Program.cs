using MedicalCenterWinForms.Forms;
using MedicalCenterWinForms.Services;

namespace MedicalCenterWinForms;

static class Program
{
    /// <summary>
    ///  The main entry point for the application.
    /// </summary>
    [STAThread]
    static async Task Main(string[] args)
    {
        // To customize application configuration such as set high DPI settings or default font,
        // see https://aka.ms/applicationconfiguration.
        ApplicationConfiguration.Initialize();

        // Enable visual styles
        Application.EnableVisualStyles();
        Application.SetCompatibleTextRenderingDefault(false);

        // Set Arabic font for the entire application
        SetArabicFont();

        // Check if we're running a specific test
        if (args.Length > 0 && args[0] == "TestLoginOnly")
        {
            // Run only login form for testing
            var testDatabaseService = new DatabaseService();
            using var loginForm = new LoginForm(testDatabaseService);
            loginForm.ShowDialog();
            return;
        }

        // Start with login form
        var databaseService = new DatabaseService();

        try
        {
            // Initialize database and default users
            await InitializeDatabaseAsync(databaseService);

            using var loginForm = new LoginForm(databaseService);
            if (loginForm.ShowDialog() == DialogResult.OK && loginForm.CurrentUser != null)
            {
                Application.Run(new MainForm(databaseService));
            }
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في تشغيل التطبيق: {ex.Message}\n\nDetails: {ex.StackTrace}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
        }
    }

    private static void SetArabicFont()
    {
        // Set default font for Arabic support
        var arabicFont = new Font("Segoe UI", 9F, FontStyle.Regular, GraphicsUnit.Point);

        // Try to use Tahoma if available (better Arabic support)
        try
        {
            arabicFont = new Font("Tahoma", 9F, FontStyle.Regular, GraphicsUnit.Point);
        }
        catch
        {
            // Fallback to Segoe UI if Tahoma is not available
            arabicFont = new Font("Segoe UI", 9F, FontStyle.Regular, GraphicsUnit.Point);
        }

        Application.SetDefaultFont(arabicFont);
    }

    private static async Task InitializeDatabaseAsync(DatabaseService databaseService)
    {
        try
        {
            // Test database connection
            if (await databaseService.TestConnectionAsync())
            {
                // Ensure default users exist
                await databaseService.EnsureDefaultUsersAsync();
            }
            else
            {
                MessageBox.Show("تعذر الاتصال بقاعدة البيانات. يرجى التحقق من الإعدادات.",
                    "خطأ في قاعدة البيانات", MessageBoxButtons.OK, MessageBoxIcon.Warning);
            }
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في تهيئة قاعدة البيانات: {ex.Message}",
                "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
        }
    }
}