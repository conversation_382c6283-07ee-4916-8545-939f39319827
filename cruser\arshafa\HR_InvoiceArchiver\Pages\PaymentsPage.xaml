<UserControl x:Class="HR_InvoiceArchiver.Pages.PaymentsPage"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             xmlns:local="clr-namespace:HR_InvoiceArchiver.Converters"
             FlowDirection="RightToLeft"
             Background="Transparent"
             Focusable="True"
             KeyDown="PaymentsPage_KeyDown">

    <UserControl.Resources>
        <!-- Converters -->
        <local:StringNullOrEmptyConverter x:Key="StringNullOrEmptyConverter"/>

        <!-- حذف جميع الأنماط المحلية للأزرار والبطاقات هنا -->

        <!-- Consistent Color Palette with MainWindow -->
        <SolidColorBrush x:Key="PrimaryColor" Color="#2C3E50"/>
        <SolidColorBrush x:Key="SecondaryColor" Color="#007BFF"/>
        <SolidColorBrush x:Key="AccentColor" Color="#28A745"/>
        <SolidColorBrush x:Key="BackgroundColor" Color="#F8F9FA"/>
        <SolidColorBrush x:Key="HeaderColor" Color="#FFFFFF"/>

        <!-- Card Gradients -->
        <LinearGradientBrush x:Key="HeaderGradient" StartPoint="0,0" EndPoint="1,1">
            <GradientStop Color="#007BFF" Offset="0"/>
            <GradientStop Color="#0056B3" Offset="1"/>
        </LinearGradientBrush>

        <LinearGradientBrush x:Key="CardGradient1" StartPoint="0,0" EndPoint="1,1">
            <GradientStop Color="#E3F2FD" Offset="0"/>
            <GradientStop Color="#BBDEFB" Offset="1"/>
        </LinearGradientBrush>

        <LinearGradientBrush x:Key="CardGradient2" StartPoint="0,0" EndPoint="1,1">
            <GradientStop Color="#E8F5E8" Offset="0"/>
            <GradientStop Color="#C8E6C9" Offset="1"/>
        </LinearGradientBrush>

        <LinearGradientBrush x:Key="CardGradient3" StartPoint="0,0" EndPoint="1,1">
            <GradientStop Color="#FFF3E0" Offset="0"/>
            <GradientStop Color="#FFE0B2" Offset="1"/>
        </LinearGradientBrush>

        <LinearGradientBrush x:Key="CardGradient4" StartPoint="0,0" EndPoint="1,1">
            <GradientStop Color="#F3E5F5" Offset="0"/>
            <GradientStop Color="#E1BEE7" Offset="1"/>
        </LinearGradientBrush>

        <!-- Modern Card Style -->
        <Style x:Key="ModernCardStyle" TargetType="materialDesign:Card">
            <Setter Property="Margin" Value="8"/>
            <Setter Property="Padding" Value="20"/>
            <Setter Property="materialDesign:ElevationAssist.Elevation" Value="Dp4"/>
            <Setter Property="Cursor" Value="Hand"/>
        </Style>

        <!-- Modern Button Style -->
        <Style x:Key="ModernActionButtonStyle" TargetType="Button">
            <Setter Property="Height" Value="45"/>
            <Setter Property="Margin" Value="8,0"/>
            <Setter Property="Padding" Value="20,0"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}"
                                CornerRadius="8"
                                Padding="{TemplateBinding Padding}">
                            <Border.Effect>
                                <DropShadowEffect Color="{Binding Background.Color, RelativeSource={RelativeSource TemplatedParent}}"
                                                Opacity="0.3" BlurRadius="8" ShadowDepth="2"/>
                            </Border.Effect>
                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                            <Border.RenderTransform>
                                <ScaleTransform/>
                            </Border.RenderTransform>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="RenderTransform">
                                    <Setter.Value>
                                        <ScaleTransform ScaleX="1.02" ScaleY="1.02"/>
                                    </Setter.Value>
                                </Setter>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
    </UserControl.Resources>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- Flat Modern Header Section -->
        <Border Grid.Row="0" Background="#FAFBFC" BorderBrush="#E9ECEF" BorderThickness="0,0,0,1" Margin="0,0,0,20">
            <Grid Margin="25,20">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <!-- Left Section - Clean Title -->
                <StackPanel Grid.Column="0" Orientation="Horizontal">
                    <Border Background="#007BFF" CornerRadius="8" Width="48" Height="48" Margin="0,0,20,0">
                        <materialDesign:PackIcon Kind="CreditCard" Width="28" Height="28"
                                               Foreground="White" HorizontalAlignment="Center" VerticalAlignment="Center"/>
                    </Border>
                    <StackPanel VerticalAlignment="Center">
                        <TextBlock Text="إدارة المدفوعات" FontSize="24" FontWeight="Bold" Foreground="#2C3E50"/>
                        <TextBlock Text="نظام شامل لإدارة المدفوعات والإيصالات" FontSize="14"
                                 Foreground="#6C757D" Margin="0,4,0,0"/>
                    </StackPanel>
                </StackPanel>

                <!-- Center Section - Quick Stats -->
                <StackPanel Grid.Column="1" Orientation="Horizontal" HorizontalAlignment="Center" VerticalAlignment="Center">
                    <Border Background="White" BorderBrush="#E9ECEF" BorderThickness="1" CornerRadius="8" Padding="16,12" Margin="10,0">
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="Receipt" Width="18" Height="18"
                                                   Foreground="#007BFF" Margin="0,0,8,0"/>
                            <StackPanel>
                                <TextBlock x:Name="HeaderTotalPaymentsText" Text="0" FontSize="16" Foreground="#2C3E50" FontWeight="Bold"/>
                                <TextBlock Text="إجمالي المدفوعات" FontSize="11" Foreground="#6C757D"/>
                            </StackPanel>
                        </StackPanel>
                    </Border>
                    <Border Background="White" BorderBrush="#E9ECEF" BorderThickness="1" CornerRadius="8" Padding="16,12" Margin="10,0">
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="CurrencyUsd" Width="18" Height="18"
                                                   Foreground="#28A745" Margin="0,0,8,0"/>
                            <StackPanel>
                                <StackPanel Orientation="Horizontal">
                                    <TextBlock x:Name="HeaderTotalAmountText" Text="0" FontSize="16" Foreground="#2C3E50" FontWeight="Bold"/>
                                    <TextBlock Text=" د.ع" FontSize="12" Foreground="#6C757D" VerticalAlignment="Bottom" Margin="2,0,0,1"/>
                                </StackPanel>
                                <TextBlock Text="إجمالي المبلغ" FontSize="11" Foreground="#6C757D"/>
                            </StackPanel>
                        </StackPanel>
                    </Border>
                </StackPanel>

                <!-- Right Section - Status -->
                <StackPanel Grid.Column="2" Orientation="Horizontal" VerticalAlignment="Center">
                    <Border Background="#E8F5E8" CornerRadius="20" Padding="12,6" Margin="5,0">
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="CheckCircle" Width="14" Height="14"
                                                   Foreground="#28A745" Margin="0,0,6,0"/>
                            <TextBlock Text="متصل" FontSize="12" Foreground="#28A745" FontWeight="SemiBold"/>
                        </StackPanel>
                    </Border>
                </StackPanel>
            </Grid>
        </Border>



        <!-- Enhanced Main Content -->
        <materialDesign:Card Grid.Row="2" Style="{StaticResource ModernCardStyle}"
                           Background="White" Margin="0" Padding="25">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>

                <!-- Compact Action Buttons -->
                <Border Grid.Row="0" Background="{StaticResource BackgroundColor}"
                        CornerRadius="8" Padding="12" Margin="0,0,0,8">
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>

                        <!-- Main Action Buttons -->
                        <StackPanel Grid.Column="0" Orientation="Horizontal" HorizontalAlignment="Left">
                            <Button x:Name="AddPaymentButton"
                                   Style="{StaticResource ModernActionButtonStyle}"
                                   Background="{StaticResource AccentColor}"
                                   Height="32" Margin="4,0"
                                   ToolTip="إضافة مدفوعة جديدة (Ctrl+N)"
                                   Click="AddPaymentButton_Click">
                                <StackPanel Orientation="Horizontal">
                                    <materialDesign:PackIcon Kind="Plus" Width="14" Height="14" Margin="0,0,5,0"/>
                                    <TextBlock Text="إضافة مدفوعة" FontSize="11"/>
                                </StackPanel>
                            </Button>

                            <Button x:Name="AddMultiPaymentButton"
                                   Style="{StaticResource ModernActionButtonStyle}"
                                   Background="{StaticResource SecondaryColor}"
                                   Height="32" Margin="4,0"
                                   ToolTip="إنشاء وصل متعدد (Ctrl+M)"
                                   Click="AddMultiPaymentButton_Click">
                                <StackPanel Orientation="Horizontal">
                                    <materialDesign:PackIcon Kind="Receipt" Width="14" Height="14" Margin="0,0,5,0"/>
                                    <TextBlock Text="وصل متعدد" FontSize="11"/>
                                </StackPanel>
                            </Button>
                        </StackPanel>

                        <!-- Secondary Action Buttons -->
                        <StackPanel Grid.Column="1" Orientation="Horizontal" HorizontalAlignment="Right">
                            <Button x:Name="RefreshButton"
                                   Style="{StaticResource MaterialDesignIconButton}"
                                   Width="35" Height="35" Margin="3,0"
                                   ToolTip="تحديث البيانات (F5 أو Ctrl+R)"
                                   Click="RefreshButton_Click">
                                <materialDesign:PackIcon Kind="Refresh" Width="18" Height="18"/>
                            </Button>

                            <Button x:Name="ExportButton"
                                   Style="{StaticResource MaterialDesignIconButton}"
                                   Width="35" Height="35" Margin="3,0"
                                   ToolTip="تصدير البيانات (Ctrl+E)"
                                   Click="ExportButton_Click">
                                <materialDesign:PackIcon Kind="FileExport" Width="18" Height="18"/>
                            </Button>

                            <Button x:Name="CompactViewToggle"
                                   Style="{StaticResource MaterialDesignIconButton}"
                                   Width="35" Height="35" Margin="3,0"
                                   ToolTip="تبديل العرض المضغوط"
                                   Click="CompactViewToggle_Click">
                                <materialDesign:PackIcon Kind="ViewCompact" Width="18" Height="18"/>
                            </Button>
                        </StackPanel>
                    </Grid>
                </Border>

                <!-- Compact Search Section -->
                <Border Grid.Row="1" Background="{StaticResource BackgroundColor}"
                        CornerRadius="8" Padding="10" Margin="0,0,0,8">
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>

                        <!-- Search Box -->
                        <TextBox Grid.Column="0" x:Name="SearchTextBox"
                                Style="{StaticResource MaterialDesignOutlinedTextBox}"
                                materialDesign:HintAssist.Hint="🔍 البحث في رقم الإيصال، المورد، أو التفاصيل... (Ctrl+F)"
                                FontSize="12" Height="32"
                                Margin="0,0,8,0"
                                TextChanged="SearchTextBox_TextChanged"/>

                        <!-- Quick Filter Buttons -->
                        <StackPanel Grid.Column="1" Orientation="Horizontal" VerticalAlignment="Center" Margin="0,0,8,0">
                            <Button x:Name="TodayFilterButton"
                                   Style="{StaticResource MaterialDesignOutlinedButton}"
                                   Content="اليوم" Height="28" Padding="8,2"
                                   FontSize="10" Margin="2,0"
                                   ToolTip="عرض مدفوعات اليوم (Ctrl+1)"
                                   Click="TodayFilter_Click"/>
                            <Button x:Name="WeekFilterButton"
                                   Style="{StaticResource MaterialDesignOutlinedButton}"
                                   Content="الأسبوع" Height="28" Padding="8,2"
                                   FontSize="10" Margin="2,0"
                                   ToolTip="عرض مدفوعات هذا الأسبوع (Ctrl+2)"
                                   Click="WeekFilter_Click"/>
                            <Button x:Name="MonthFilterButton"
                                   Style="{StaticResource MaterialDesignOutlinedButton}"
                                   Content="الشهر" Height="28" Padding="8,2"
                                   FontSize="10" Margin="2,0"
                                   ToolTip="عرض مدفوعات هذا الشهر (Ctrl+3)"
                                   Click="MonthFilter_Click"/>
                        </StackPanel>

                        <!-- Action Buttons -->
                        <StackPanel Grid.Column="2" Orientation="Horizontal" VerticalAlignment="Center">
                            <Button Style="{StaticResource MaterialDesignIconButton}"
                                   Width="28" Height="28" Margin="1,0"
                                   ToolTip="مسح البحث"
                                   Click="ClearSearchButton_Click">
                                <materialDesign:PackIcon Kind="Close" Width="14" Height="14"/>
                            </Button>

                            <Button x:Name="ToggleFiltersButton"
                                   Style="{StaticResource MaterialDesignIconButton}"
                                   Width="28" Height="28" Margin="1,0"
                                   ToolTip="فلترة متقدمة"
                                   Click="ToggleFilters_Click">
                                <materialDesign:PackIcon Kind="FilterVariant" Width="14" Height="14"/>
                            </Button>

                            <Button x:Name="KeyboardShortcutsButton"
                                   Style="{StaticResource MaterialDesignIconButton}"
                                   Width="28" Height="28" Margin="1,0"
                                   ToolTip="عرض اختصارات لوحة المفاتيح"
                                   Click="KeyboardShortcuts_Click">
                                <materialDesign:PackIcon Kind="Keyboard" Width="14" Height="14"/>
                            </Button>
                        </StackPanel>
                    </Grid>
                </Border>

                <!-- Advanced Filters Panel -->
                <Border Grid.Row="1" x:Name="FiltersPanel" Background="White" CornerRadius="8"
                       Padding="20" Visibility="Collapsed" Margin="0,0,0,15"
                       BorderBrush="#E9ECEF" BorderThickness="1">
                            <Grid>
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                </Grid.RowDefinitions>

                                <!-- Filter Title -->
                                <TextBlock Grid.Row="0" Text="تصفية متقدمة" FontWeight="Bold" FontSize="16"
                                         Foreground="{StaticResource PrimaryColor}" Margin="0,0,0,15"/>

                                <!-- Filter Controls -->
                                <Grid Grid.Row="1" Margin="0,0,0,20">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="*"/>
                                    </Grid.ColumnDefinitions>
                                    <Grid.RowDefinitions>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="Auto"/>
                                    </Grid.RowDefinitions>

                                    <!-- Date Range -->
                                    <StackPanel Grid.Row="0" Grid.Column="0" Margin="0,0,15,15">
                                        <TextBlock Text="من تاريخ:" FontWeight="SemiBold" Margin="0,0,0,5"/>
                                        <DatePicker x:Name="FromDatePicker"
                                                  Style="{StaticResource MaterialDesignOutlinedDatePicker}"
                                                  materialDesign:HintAssist.Hint="اختر التاريخ"
                                                  SelectedDateChanged="DateFilter_Changed"/>
                                    </StackPanel>

                                    <StackPanel Grid.Row="0" Grid.Column="1" Margin="0,0,15,15">
                                        <TextBlock Text="إلى تاريخ:" FontWeight="SemiBold" Margin="0,0,0,5"/>
                                        <DatePicker x:Name="ToDatePicker"
                                                  Style="{StaticResource MaterialDesignOutlinedDatePicker}"
                                                  materialDesign:HintAssist.Hint="اختر التاريخ"
                                                  SelectedDateChanged="DateFilter_Changed"/>
                                    </StackPanel>

                                    <!-- Amount Range -->
                                    <StackPanel Grid.Row="0" Grid.Column="2" Margin="0,0,15,15">
                                        <TextBlock Text="المبلغ من:" FontWeight="SemiBold" Margin="0,0,0,5"/>
                                        <TextBox x:Name="MinAmountTextBox"
                                               Style="{StaticResource MaterialDesignOutlinedTextBox}"
                                               materialDesign:HintAssist.Hint="أقل مبلغ"
                                               TextChanged="AmountFilter_Changed"/>
                                    </StackPanel>

                                    <StackPanel Grid.Row="0" Grid.Column="3" Margin="0,0,0,15">
                                        <TextBlock Text="المبلغ إلى:" FontWeight="SemiBold" Margin="0,0,0,5"/>
                                        <TextBox x:Name="MaxAmountTextBox"
                                               Style="{StaticResource MaterialDesignOutlinedTextBox}"
                                               materialDesign:HintAssist.Hint="أعلى مبلغ"
                                               TextChanged="AmountFilter_Changed"/>
                                    </StackPanel>

                                    <!-- Payment Method and Status -->
                                    <StackPanel Grid.Row="1" Grid.Column="0" Margin="0,0,15,0">
                                        <TextBlock Text="طريقة الدفع:" FontWeight="SemiBold" Margin="0,0,0,5"/>
                                        <ComboBox x:Name="PaymentMethodComboBox"
                                                Style="{StaticResource MaterialDesignOutlinedComboBox}"
                                                materialDesign:HintAssist.Hint="اختر طريقة الدفع"
                                                SelectionChanged="PaymentMethodFilter_Changed">
                                            <ComboBoxItem Content="جميع الطرق" Tag="All"/>
                                            <ComboBoxItem Content="نقدي" Tag="Cash"/>
                                            <ComboBoxItem Content="شيك" Tag="Check"/>
                                            <ComboBoxItem Content="تحويل بنكي" Tag="BankTransfer"/>
                                        </ComboBox>
                                    </StackPanel>

                                    <StackPanel Grid.Row="1" Grid.Column="1" Margin="0,0,15,0">
                                        <TextBlock Text="حالة الدفع:" FontWeight="SemiBold" Margin="0,0,0,5"/>
                                        <ComboBox x:Name="PaymentStatusComboBox"
                                                Style="{StaticResource MaterialDesignOutlinedComboBox}"
                                                materialDesign:HintAssist.Hint="اختر حالة الدفع"
                                                SelectionChanged="PaymentStatusFilter_Changed">
                                            <ComboBoxItem Content="جميع الحالات" Tag="All"/>
                                            <ComboBoxItem Content="دفعة جزئية" Tag="PartialPayment"/>
                                            <ComboBoxItem Content="دفعة كاملة" Tag="FullPayment"/>
                                            <ComboBoxItem Content="دفعة زائدة" Tag="OverPayment"/>
                                        </ComboBox>
                                    </StackPanel>
                                </Grid>

                                <!-- Filter Actions -->
                                <StackPanel Grid.Row="2" Orientation="Horizontal" HorizontalAlignment="Center">
                                    <Button x:Name="ApplyFiltersButton"
                                          Style="{StaticResource MaterialDesignRaisedButton}"
                                          Background="{StaticResource AccentColor}"
                                          Foreground="White"
                                          Width="120"
                                          Height="35"
                                          Margin="0,0,10,0"
                                          Click="ApplyFilters_Click">
                                        <StackPanel Orientation="Horizontal">
                                            <materialDesign:PackIcon Kind="FilterCheck" Width="16" Height="16" Margin="0,0,5,0"/>
                                            <TextBlock Text="تطبيق" FontWeight="SemiBold"/>
                                        </StackPanel>
                                    </Button>

                                    <Button x:Name="ClearFiltersButton"
                                          Style="{StaticResource MaterialDesignOutlinedButton}"
                                          BorderBrush="#6C757D"
                                          Foreground="#6C757D"
                                          Width="120"
                                          Height="35"
                                          Click="ClearFilters_Click">
                                        <StackPanel Orientation="Horizontal">
                                            <materialDesign:PackIcon Kind="FilterRemove" Width="16" Height="16" Margin="0,0,5,0"/>
                                            <TextBlock Text="مسح الكل" FontWeight="SemiBold"/>
                                        </StackPanel>
                                    </Button>
                                </StackPanel>
                            </Grid>
                </Border>

                <!-- Enhanced Payments DataGrid -->
                <Border Grid.Row="2" Background="White" CornerRadius="12"
                        BorderBrush="#E9ECEF" BorderThickness="1" Margin="0,0,0,20">
                    <Border.Effect>
                        <DropShadowEffect Color="#000000" Opacity="0.1" BlurRadius="10" ShadowDepth="2"/>
                    </Border.Effect>
                    <Grid>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="*"/>
                        </Grid.RowDefinitions>

                        <!-- Enhanced Table Header -->
                        <Border Grid.Row="0" Background="#F8F9FA" CornerRadius="12,12,0,0" Padding="20,15">
                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="Auto"/>
                                </Grid.ColumnDefinitions>

                                <StackPanel Grid.Column="0" Orientation="Horizontal" VerticalAlignment="Center">
                                    <materialDesign:PackIcon Kind="Table" Width="18" Height="18" Margin="0,0,10,0" Foreground="#007BFF"/>
                                    <TextBlock Text="جدول المدفوعات" FontSize="16" FontWeight="SemiBold" Foreground="#2C3E50" Margin="0,0,15,0"/>
                                    <Border Background="#E3F2FD" CornerRadius="12" Padding="8,4">
                                        <TextBlock x:Name="PaymentCountText" Text="0 دفعة" FontSize="12" FontWeight="SemiBold" Foreground="#007BFF"/>
                                    </Border>
                                </StackPanel>

                                <StackPanel Grid.Column="1" Orientation="Horizontal" VerticalAlignment="Center">
                                    <TextBlock Text="العرض المضغوط" FontSize="12" Margin="0,0,8,0" VerticalAlignment="Center" Foreground="#6C757D"/>
                                    <ToggleButton x:Name="CompactModeToggle"
                                                Style="{StaticResource MaterialDesignSwitchToggleButton}"
                                                ToolTip="تفعيل/إلغاء العرض المضغوط"
                                                IsChecked="False"
                                                Checked="CompactMode_Changed"
                                                Unchecked="CompactMode_Changed"/>
                                </StackPanel>
                            </Grid>
                        </Border>

                        <DataGrid Grid.Row="1" x:Name="PaymentsDataGrid"
                                 AutoGenerateColumns="False"
                                 CanUserAddRows="False"
                                 CanUserDeleteRows="False"
                                 IsReadOnly="True"
                                 SelectionMode="Single"
                                 GridLinesVisibility="Horizontal"
                                 HeadersVisibility="Column"
                                 Background="Transparent"
                                 AlternatingRowBackground="#FAFBFC"
                                 RowHeight="55"
                                 FontSize="13"
                                 materialDesign:DataGridAssist.CellPadding="15,10"
                                 materialDesign:DataGridAssist.ColumnHeaderPadding="15,12"
                                 BorderThickness="0"
                                 CanUserSortColumns="True"
                                 CanUserReorderColumns="True"
                                 CanUserResizeColumns="True"
                                 EnableRowVirtualization="True"
                                 EnableColumnVirtualization="True">

                            <DataGrid.Resources>
                                <Style TargetType="DataGridColumnHeader">
                                    <Setter Property="Background" Value="#007BFF"/>
                                    <Setter Property="Foreground" Value="White"/>
                                    <Setter Property="FontWeight" Value="SemiBold"/>
                                    <Setter Property="FontSize" Value="13"/>
                                    <Setter Property="Height" Value="45"/>
                                    <Setter Property="HorizontalContentAlignment" Value="Center"/>
                                    <Setter Property="VerticalContentAlignment" Value="Center"/>
                                    <Setter Property="BorderBrush" Value="#0056B3"/>
                                    <Setter Property="BorderThickness" Value="0,0,1,0"/>
                                </Style>

                                <Style TargetType="DataGridRow">
                                    <Setter Property="Background" Value="Transparent"/>
                                    <Style.Triggers>
                                        <Trigger Property="IsMouseOver" Value="True">
                                            <Setter Property="Background" Value="#E3F2FD"/>
                                        </Trigger>
                                        <Trigger Property="IsSelected" Value="True">
                                            <Setter Property="Background" Value="#BBDEFB"/>
                                        </Trigger>
                                    </Style.Triggers>
                                </Style>
                            </DataGrid.Resources>

                        <DataGrid.Columns>
                            <DataGridTextColumn Header="رقم الإيصال" Binding="{Binding ReceiptNumber}" Width="140"
                                              SortDirection="Descending" CanUserSort="True">
                                <DataGridTextColumn.ElementStyle>
                                    <Style TargetType="TextBlock">
                                        <Setter Property="FontWeight" Value="Bold"/>
                                        <Setter Property="Foreground" Value="#007BFF"/>
                                        <Setter Property="HorizontalAlignment" Value="Center"/>
                                        <Setter Property="VerticalAlignment" Value="Center"/>
                                        <Setter Property="FontSize" Value="13"/>
                                    </Style>
                                </DataGridTextColumn.ElementStyle>
                            </DataGridTextColumn>

                            <DataGridTextColumn Header="المورد" Binding="{Binding SupplierName}" Width="*" CanUserSort="True" MinWidth="200">
                                <DataGridTextColumn.ElementStyle>
                                    <Style TargetType="TextBlock">
                                        <Setter Property="FontWeight" Value="SemiBold"/>
                                        <Setter Property="Foreground" Value="#2C3E50"/>
                                        <Setter Property="HorizontalAlignment" Value="Right"/>
                                        <Setter Property="VerticalAlignment" Value="Center"/>
                                        <Setter Property="TextTrimming" Value="CharacterEllipsis"/>
                                        <Setter Property="ToolTip" Value="{Binding SupplierName}"/>
                                        <Setter Property="Margin" Value="10,0"/>
                                        <Setter Property="FontSize" Value="13"/>
                                    </Style>
                                </DataGridTextColumn.ElementStyle>
                            </DataGridTextColumn>

                            <DataGridTextColumn Header="تاريخ الدفع" Binding="{Binding PaymentDate, StringFormat=dd/MM/yyyy}" Width="120" CanUserSort="True">
                                <DataGridTextColumn.ElementStyle>
                                    <Style TargetType="TextBlock">
                                        <Setter Property="Foreground" Value="#6C757D"/>
                                        <Setter Property="HorizontalAlignment" Value="Center"/>
                                        <Setter Property="VerticalAlignment" Value="Center"/>
                                        <Setter Property="FontWeight" Value="Medium"/>
                                        <Setter Property="FontSize" Value="12"/>
                                    </Style>
                                </DataGridTextColumn.ElementStyle>
                            </DataGridTextColumn>

                            <DataGridTextColumn Header="المبلغ (د.ع)" Binding="{Binding Amount, StringFormat=N0}" Width="130" CanUserSort="True">
                                <DataGridTextColumn.ElementStyle>
                                    <Style TargetType="TextBlock">
                                        <Setter Property="FontWeight" Value="Bold"/>
                                        <Setter Property="Foreground" Value="#28A745"/>
                                        <Setter Property="HorizontalAlignment" Value="Center"/>
                                        <Setter Property="VerticalAlignment" Value="Center"/>
                                        <Setter Property="FontSize" Value="14"/>
                                    </Style>
                                </DataGridTextColumn.ElementStyle>
                            </DataGridTextColumn>

                            <DataGridTextColumn Header="الخصم" Binding="{Binding DiscountAmount, StringFormat=N0}" Width="100" CanUserSort="True">
                                <DataGridTextColumn.ElementStyle>
                                    <Style TargetType="TextBlock">
                                        <Setter Property="FontWeight" Value="SemiBold"/>
                                        <Setter Property="Foreground" Value="#DC3545"/>
                                        <Setter Property="HorizontalAlignment" Value="Center"/>
                                        <Setter Property="VerticalAlignment" Value="Center"/>
                                        <Setter Property="FontSize" Value="12"/>
                                    </Style>
                                </DataGridTextColumn.ElementStyle>
                            </DataGridTextColumn>

                            <DataGridTextColumn Header="الاسترجاع" Binding="{Binding RefundValue, StringFormat=N0}" Width="100" CanUserSort="True">
                                <DataGridTextColumn.ElementStyle>
                                    <Style TargetType="TextBlock">
                                        <Setter Property="FontWeight" Value="SemiBold"/>
                                        <Setter Property="Foreground" Value="#6F42C1"/>
                                        <Setter Property="HorizontalAlignment" Value="Center"/>
                                        <Setter Property="VerticalAlignment" Value="Center"/>
                                        <Setter Property="FontSize" Value="12"/>
                                    </Style>
                                </DataGridTextColumn.ElementStyle>
                            </DataGridTextColumn>

                            <DataGridTemplateColumn Header="حالة التسديد" Width="140">
                                <DataGridTemplateColumn.CellTemplate>
                                    <DataTemplate>
                                        <Border CornerRadius="15" Padding="10,6" HorizontalAlignment="Center">
                                            <Border.Style>
                                                <Style TargetType="Border">
                                                    <Style.Triggers>
                                                        <DataTrigger Binding="{Binding Status}" Value="FullPayment">
                                                            <Setter Property="Background" Value="#D4EDDA"/>
                                                        </DataTrigger>
                                                        <DataTrigger Binding="{Binding Status}" Value="PartialPayment">
                                                            <Setter Property="Background" Value="#FFF3CD"/>
                                                        </DataTrigger>
                                                        <DataTrigger Binding="{Binding Status}" Value="PaymentWithDiscount">
                                                            <Setter Property="Background" Value="#F8D7DA"/>
                                                        </DataTrigger>
                                                        <DataTrigger Binding="{Binding Status}" Value="PaymentWithRefund">
                                                            <Setter Property="Background" Value="#D1ECF1"/>
                                                        </DataTrigger>
                                                    </Style.Triggers>
                                                </Style>
                                            </Border.Style>
                                            <TextBlock Text="{Binding StatusText}" FontSize="12" FontWeight="SemiBold" HorizontalAlignment="Center">
                                                <TextBlock.Style>
                                                    <Style TargetType="TextBlock">
                                                        <Style.Triggers>
                                                            <DataTrigger Binding="{Binding Status}" Value="FullPayment">
                                                                <Setter Property="Foreground" Value="#155724"/>
                                                            </DataTrigger>
                                                            <DataTrigger Binding="{Binding Status}" Value="PartialPayment">
                                                                <Setter Property="Foreground" Value="#856404"/>
                                                            </DataTrigger>
                                                            <DataTrigger Binding="{Binding Status}" Value="PaymentWithDiscount">
                                                                <Setter Property="Foreground" Value="#721C24"/>
                                                            </DataTrigger>
                                                            <DataTrigger Binding="{Binding Status}" Value="PaymentWithRefund">
                                                                <Setter Property="Foreground" Value="#0C5460"/>
                                                            </DataTrigger>
                                                        </Style.Triggers>
                                                    </Style>
                                                </TextBlock.Style>
                                            </TextBlock>
                                        </Border>
                                    </DataTemplate>
                                </DataGridTemplateColumn.CellTemplate>
                            </DataGridTemplateColumn>

                            <DataGridTemplateColumn Header="طريقة الدفع" Width="130">
                                <DataGridTemplateColumn.CellTemplate>
                                    <DataTemplate>
                                        <Border CornerRadius="12" Padding="8,4" HorizontalAlignment="Center">
                                            <Border.Style>
                                                <Style TargetType="Border">
                                                    <Style.Triggers>
                                                        <DataTrigger Binding="{Binding PaymentMethodText}" Value="نقدي">
                                                            <Setter Property="Background" Value="#FFF3E0"/>
                                                        </DataTrigger>
                                                        <DataTrigger Binding="{Binding PaymentMethodText}" Value="بطاقة ائتمان">
                                                            <Setter Property="Background" Value="#F3E5F5"/>
                                                        </DataTrigger>
                                                    </Style.Triggers>
                                                </Style>
                                            </Border.Style>
                                            <TextBlock Text="{Binding PaymentMethodText}" FontSize="11" FontWeight="SemiBold">
                                                <TextBlock.Style>
                                                    <Style TargetType="TextBlock">
                                                        <Style.Triggers>
                                                            <DataTrigger Binding="{Binding PaymentMethodText}" Value="نقدي">
                                                                <Setter Property="Foreground" Value="#F57C00"/>
                                                            </DataTrigger>
                                                            <DataTrigger Binding="{Binding PaymentMethodText}" Value="بطاقة ائتمان">
                                                                <Setter Property="Foreground" Value="#7B1FA2"/>
                                                            </DataTrigger>
                                                        </Style.Triggers>
                                                    </Style>
                                                </TextBlock.Style>
                                            </TextBlock>
                                        </Border>
                                    </DataTemplate>
                                </DataGridTemplateColumn.CellTemplate>
                            </DataGridTemplateColumn>

                            <DataGridTextColumn Header="المتبقي" Binding="{Binding Invoice.RemainingAmount, StringFormat=N0}" Width="110">
                                <DataGridTextColumn.ElementStyle>
                                    <Style TargetType="TextBlock">
                                        <Setter Property="FontWeight" Value="Bold"/>
                                        <Setter Property="Foreground" Value="#DC3545"/>
                                        <Setter Property="HorizontalAlignment" Value="Center"/>
                                        <Setter Property="VerticalAlignment" Value="Center"/>
                                        <Setter Property="FontSize" Value="12"/>
                                    </Style>
                                </DataGridTextColumn.ElementStyle>
                            </DataGridTextColumn>

                            <DataGridTextColumn Header="التفاصيل" Binding="{Binding Details}" Width="*">
                                <DataGridTextColumn.ElementStyle>
                                    <Style TargetType="TextBlock">
                                        <Setter Property="Foreground" Value="#6C757D"/>
                                        <Setter Property="TextWrapping" Value="Wrap"/>
                                        <Setter Property="HorizontalAlignment" Value="Right"/>
                                        <Setter Property="VerticalAlignment" Value="Center"/>
                                        <Setter Property="Margin" Value="10,0"/>
                                        <Setter Property="FontSize" Value="12"/>
                                    </Style>
                                </DataGridTextColumn.ElementStyle>
                            </DataGridTextColumn>

                            <DataGridTemplateColumn Header="الإجراءات" Width="180" CanUserSort="False" CanUserReorder="False">
                                <DataGridTemplateColumn.CellTemplate>
                                    <DataTemplate>
                                        <StackPanel Orientation="Horizontal" HorizontalAlignment="Center" VerticalAlignment="Center">
                                            <!-- View Receipt Attachment Button -->
                                            <Button ToolTip="عرض مستند الوصل"
                                                   Width="32" Height="32"
                                                   Margin="3"
                                                   Tag="{Binding}"
                                                   Click="ViewReceiptAttachment_Click">
                                                <Button.Style>
                                                    <Style TargetType="Button" BasedOn="{StaticResource MaterialDesignIconButton}">
                                                        <Setter Property="Background" Value="#FFF3E0"/>
                                                        <Setter Property="BorderBrush" Value="#FF6B35"/>
                                                        <Setter Property="BorderThickness" Value="1"/>
                                                        <Style.Triggers>
                                                            <DataTrigger Binding="{Binding AttachmentPath, Converter={StaticResource StringNullOrEmptyConverter}}" Value="True">
                                                                <Setter Property="Visibility" Value="Collapsed"/>
                                                            </DataTrigger>
                                                            <Trigger Property="IsMouseOver" Value="True">
                                                                <Setter Property="Background" Value="#FF6B35"/>
                                                            </Trigger>
                                                        </Style.Triggers>
                                                    </Style>
                                                </Button.Style>
                                                <materialDesign:PackIcon Kind="FileDocument" Width="16" Height="16"
                                                                       Foreground="#FF6B35"/>
                                            </Button>

                                            <!-- View Details Button -->
                                            <Button ToolTip="عرض التفاصيل"
                                                   Width="32" Height="32"
                                                   Margin="3"
                                                   Tag="{Binding}"
                                                   Click="ViewPaymentDetails_Click">
                                                <Button.Style>
                                                    <Style TargetType="Button" BasedOn="{StaticResource MaterialDesignIconButton}">
                                                        <Setter Property="Background" Value="#E3F2FD"/>
                                                        <Setter Property="BorderBrush" Value="#007BFF"/>
                                                        <Setter Property="BorderThickness" Value="1"/>
                                                        <Style.Triggers>
                                                            <Trigger Property="IsMouseOver" Value="True">
                                                                <Setter Property="Background" Value="#007BFF"/>
                                                            </Trigger>
                                                        </Style.Triggers>
                                                    </Style>
                                                </Button.Style>
                                                <materialDesign:PackIcon Kind="InformationOutline" Width="16" Height="16"
                                                                       Foreground="#007BFF"/>
                                            </Button>



                                            <!-- Edit Button -->
                                            <Button ToolTip="تعديل"
                                                   Width="32" Height="32"
                                                   Margin="3"
                                                   Tag="{Binding}"
                                                   Click="EditPayment_Click">
                                                <Button.Style>
                                                    <Style TargetType="Button" BasedOn="{StaticResource MaterialDesignIconButton}">
                                                        <Setter Property="Background" Value="#D4EDDA"/>
                                                        <Setter Property="BorderBrush" Value="#28A745"/>
                                                        <Setter Property="BorderThickness" Value="1"/>
                                                        <Style.Triggers>
                                                            <Trigger Property="IsMouseOver" Value="True">
                                                                <Setter Property="Background" Value="#28A745"/>
                                                            </Trigger>
                                                        </Style.Triggers>
                                                    </Style>
                                                </Button.Style>
                                                <materialDesign:PackIcon Kind="Edit" Width="16" Height="16"
                                                                       Foreground="#28A745"/>
                                            </Button>

                                            <!-- Delete Button -->
                                            <Button ToolTip="حذف"
                                                   Width="32" Height="32"
                                                   Margin="3"
                                                   Tag="{Binding}"
                                                   Click="DeletePayment_Click">
                                                <Button.Style>
                                                    <Style TargetType="Button" BasedOn="{StaticResource MaterialDesignIconButton}">
                                                        <Setter Property="Background" Value="#F8D7DA"/>
                                                        <Setter Property="BorderBrush" Value="#DC3545"/>
                                                        <Setter Property="BorderThickness" Value="1"/>
                                                        <Style.Triggers>
                                                            <Trigger Property="IsMouseOver" Value="True">
                                                                <Setter Property="Background" Value="#DC3545"/>
                                                            </Trigger>
                                                        </Style.Triggers>
                                                    </Style>
                                                </Button.Style>
                                                <materialDesign:PackIcon Kind="Delete" Width="16" Height="16"
                                                                       Foreground="#DC3545"/>
                                            </Button>
                                        </StackPanel>
                                    </DataTemplate>
                                </DataGridTemplateColumn.CellTemplate>
                            </DataGridTemplateColumn>
                        </DataGrid.Columns>
                    </DataGrid>
                </Grid>
            </Border>

                <!-- Enhanced Footer -->
                <Border Grid.Row="3" Background="{StaticResource BackgroundColor}"
                        CornerRadius="12" Padding="20" Margin="0,20,0,0">
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>

                        <StackPanel Grid.Column="0" Orientation="Horizontal">
                            <Border Background="{StaticResource SecondaryColor}" CornerRadius="20"
                                    Width="35" Height="35" Margin="0,0,12,0">
                                <materialDesign:PackIcon Kind="Information" Width="18" Height="18"
                                                       Foreground="White" HorizontalAlignment="Center" VerticalAlignment="Center"/>
                            </Border>
                            <StackPanel VerticalAlignment="Center">
                                <TextBlock Text="المجموع المعروض" FontSize="12" Foreground="#6C757D"/>
                                <TextBlock x:Name="FooterPaymentCountText" Text="0 دفعة" FontSize="16"
                                         FontWeight="Bold" Foreground="{StaticResource SecondaryColor}"/>
                            </StackPanel>
                        </StackPanel>

                        <StackPanel Grid.Column="1" Orientation="Horizontal" HorizontalAlignment="Center">
                            <Border Background="{StaticResource AccentColor}" CornerRadius="20"
                                    Width="35" Height="35" Margin="0,0,12,0">
                                <materialDesign:PackIcon Kind="CurrencyUsd" Width="18" Height="18"
                                                       Foreground="White" HorizontalAlignment="Center" VerticalAlignment="Center"/>
                            </Border>
                            <StackPanel VerticalAlignment="Center">
                                <TextBlock Text="إجمالي المبلغ" FontSize="12" Foreground="#6C757D"/>
                                <TextBlock x:Name="TotalDisplayedAmountText" Text="0 د.ع" FontSize="16"
                                         FontWeight="Bold" Foreground="{StaticResource AccentColor}"/>
                            </StackPanel>
                        </StackPanel>

                        <StackPanel Grid.Column="2" Orientation="Horizontal" HorizontalAlignment="Right">
                            <Border Background="#17A2B8" CornerRadius="20"
                                    Width="35" Height="35" Margin="0,0,12,0">
                                <materialDesign:PackIcon Kind="Update" Width="18" Height="18"
                                                       Foreground="White" HorizontalAlignment="Center" VerticalAlignment="Center"/>
                            </Border>
                            <StackPanel VerticalAlignment="Center">
                                <TextBlock Text="آخر تحديث" FontSize="12" Foreground="#6C757D"/>
                                <TextBlock x:Name="LastUpdateText" Text="الآن" FontSize="16"
                                         FontWeight="Bold" Foreground="#17A2B8"/>
                            </StackPanel>
                        </StackPanel>
                    </Grid>
                </Border>
            </Grid>
        </materialDesign:Card>

        <!-- Enhanced Loading Panel -->
        <Grid x:Name="LoadingPanel"
              Grid.Row="2"
              Visibility="Collapsed"
              Background="#F2F8F9FA">
            <materialDesign:Card HorizontalAlignment="Center" VerticalAlignment="Center"
                               Padding="50" Style="{StaticResource ModernCardStyle}">
                <StackPanel HorizontalAlignment="Center">
                    <Border Background="{StaticResource SecondaryColor}" CornerRadius="50"
                            Width="80" Height="80" Margin="0,0,0,25">
                        <materialDesign:PackIcon Kind="Loading" Width="40" Height="40"
                                               Foreground="White" HorizontalAlignment="Center" VerticalAlignment="Center">
                            <materialDesign:PackIcon.RenderTransform>
                                <RotateTransform/>
                            </materialDesign:PackIcon.RenderTransform>
                            <materialDesign:PackIcon.Triggers>
                                <EventTrigger RoutedEvent="Loaded">
                                    <BeginStoryboard>
                                        <Storyboard RepeatBehavior="Forever">
                                            <DoubleAnimation Storyboard.TargetProperty="RenderTransform.Angle"
                                                           From="0" To="360" Duration="0:0:2"/>
                                        </Storyboard>
                                    </BeginStoryboard>
                                </EventTrigger>
                            </materialDesign:PackIcon.Triggers>
                        </materialDesign:PackIcon>
                    </Border>
                    <TextBlock Text="جاري تحميل المدفوعات..." FontSize="20" FontWeight="SemiBold"
                             Foreground="{StaticResource SecondaryColor}" HorizontalAlignment="Center" Margin="0,0,0,10"/>
                    <TextBlock Text="يرجى الانتظار..." FontSize="14" Foreground="#6C757D" HorizontalAlignment="Center"/>
                    <ProgressBar IsIndeterminate="True" Width="250" Height="6" Margin="0,20,0,0"
                               Foreground="{StaticResource SecondaryColor}"/>
                </StackPanel>
            </materialDesign:Card>
        </Grid>

        <!-- Enhanced Empty State Panel -->
        <Grid x:Name="EmptyStatePanel"
              Grid.Row="2"
              Visibility="Collapsed"
              Background="#F2F8F9FA">
            <materialDesign:Card HorizontalAlignment="Center" VerticalAlignment="Center"
                               Padding="60" Style="{StaticResource ModernCardStyle}">
                <StackPanel HorizontalAlignment="Center">
                    <Border Background="#F8F9FA" CornerRadius="50" Width="100" Height="100" Margin="0,0,0,25">
                        <materialDesign:PackIcon Kind="CreditCardOff" Width="50" Height="50"
                                               Foreground="#6C757D" HorizontalAlignment="Center" VerticalAlignment="Center"/>
                    </Border>
                    <TextBlock Text="لا توجد مدفوعات" FontSize="24" FontWeight="Bold"
                             Foreground="{StaticResource PrimaryColor}" HorizontalAlignment="Center" Margin="0,0,0,10"/>
                    <TextBlock Text="لم يتم العثور على أي مدفوعات مطابقة للبحث" FontSize="16"
                             Foreground="#6C757D" HorizontalAlignment="Center" Margin="0,0,0,25"/>

                    <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                        <Button Style="{StaticResource ModernActionButtonStyle}"
                               Background="{StaticResource AccentColor}"
                               Margin="10,0"
                               Click="AddPaymentButton_Click">
                            <StackPanel Orientation="Horizontal">
                                <materialDesign:PackIcon Kind="Plus" Width="18" Height="18" Margin="0,0,8,0"/>
                                <TextBlock Text="إضافة أول مدفوعة"/>
                            </StackPanel>
                        </Button>

                        <Button Style="{StaticResource ModernActionButtonStyle}"
                               Background="#17A2B8"
                               Margin="10,0"
                               Click="ClearSearchButton_Click">
                            <StackPanel Orientation="Horizontal">
                                <materialDesign:PackIcon Kind="FilterRemove" Width="18" Height="18" Margin="0,0,8,0"/>
                                <TextBlock Text="مسح الفلاتر"/>
                            </StackPanel>
                        </Button>
                    </StackPanel>
                </StackPanel>
            </materialDesign:Card>
        </Grid>

        <!-- Multi-Payment Overlay -->
        <Grid x:Name="MultiPaymentOverlay"
              Grid.RowSpan="3"
              Visibility="Collapsed"
              Background="#80000000">
            <Border Background="Transparent"
                    CornerRadius="12"
                    MaxWidth="1600"
                    MaxHeight="1000"
                    MinWidth="1400"
                    MinHeight="900"
                    HorizontalAlignment="Center"
                    VerticalAlignment="Center"
                    Margin="20">
                <Border.Effect>
                    <DropShadowEffect Color="Black" Opacity="0.3" BlurRadius="20" ShadowDepth="5"/>
                </Border.Effect>

                <Grid x:Name="MultiPaymentContainer">
                    <!-- MultiPaymentFormControl will be added here dynamically -->
                </Grid>
            </Border>
        </Grid>

        <!-- Payment Details Overlay -->
        <Grid x:Name="PaymentDetailsOverlay"
              Grid.RowSpan="3"
              Visibility="Collapsed"
              Background="#80000000">
            <Border Background="White"
                    CornerRadius="12"
                    MaxWidth="800"
                    MaxHeight="900"
                    MinWidth="700"
                    MinHeight="600"
                    HorizontalAlignment="Center"
                    VerticalAlignment="Center"
                    Margin="20">
                <Border.Effect>
                    <DropShadowEffect Color="Black" Opacity="0.3" BlurRadius="20" ShadowDepth="5"/>
                </Border.Effect>

                <Grid x:Name="PaymentDetailsContainer">
                    <!-- PaymentDetailsControl will be added here dynamically -->
                </Grid>
            </Border>
        </Grid>
    </Grid>
</UserControl>
