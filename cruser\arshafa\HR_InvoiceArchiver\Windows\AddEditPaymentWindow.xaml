<Window x:Class="HR_InvoiceArchiver.Windows.AddEditPaymentWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        Title="إضافة/تعديل مدفوعة"
        Height="580" Width="520"
        WindowStartupLocation="CenterOwner"
        FlowDirection="RightToLeft"
        Background="{DynamicResource MaterialDesignPaper}"
        TextElement.Foreground="{DynamicResource MaterialDesignBody}"
        TextElement.FontWeight="Regular"
        TextElement.FontSize="13"
        TextOptions.TextFormattingMode="Ideal"
        TextOptions.TextRenderingMode="Auto"
        FontFamily="{DynamicResource MaterialDesignFont}">

    <Window.Resources>
        <BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter"/>
        
        <Style x:Key="PaymentCardStyle" TargetType="materialDesign:Card">
            <Setter Property="Padding" Value="24"/>
            <Setter Property="Margin" Value="8"/>
            <Setter Property="materialDesign:ElevationAssist.Elevation" Value="Dp2"/>
        </Style>
    </Window.Resources>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <materialDesign:ColorZone Grid.Row="0" Mode="PrimaryMid" Padding="16,12">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>
                
                <materialDesign:PackIcon Grid.Column="0" Kind="CreditCard" Width="24" Height="24" Margin="0,0,12,0"/>
                <TextBlock Grid.Column="1" Text="{Binding WindowTitle}" FontSize="16" FontWeight="Medium" VerticalAlignment="Center"/>
            </Grid>
        </materialDesign:ColorZone>

        <!-- Main Content -->
        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto">
            <materialDesign:Card Style="{StaticResource PaymentCardStyle}">
                <StackPanel>
                    <!-- Invoice Selection - Full Width -->
                    <TextBlock Text="الفاتورة *" FontWeight="Medium" Margin="0,0,0,8"/>
                    <ComboBox x:Name="InvoiceComboBox"
                            Style="{StaticResource MaterialDesignOutlinedComboBox}"
                            materialDesign:HintAssist.Hint="اختر الفاتورة"
                            DisplayMemberPath="DisplayText"
                            SelectedValuePath="Id"
                            SelectionChanged="InvoiceComboBox_SelectionChanged"
                            Margin="0,0,0,12"/>

                    <!-- Invoice Info Panel -->
                    <Border x:Name="InvoiceInfoPanel"
                          Background="{DynamicResource MaterialDesignCardBackground}"
                          CornerRadius="8" Padding="12" Margin="0,0,0,12"
                          Visibility="Collapsed">
                        <StackPanel>
                            <TextBlock Text="معلومات الفاتورة" FontWeight="Medium" Margin="0,0,0,6"/>
                            <TextBlock x:Name="InvoiceInfoTextBlock" TextWrapping="Wrap"
                                     Foreground="{DynamicResource MaterialDesignBodyLight}"/>
                        </StackPanel>
                    </Border>

                    <!-- Two Column Layout for Main Fields -->
                    <Grid Margin="0,0,0,12">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="15"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>

                        <!-- Left Column -->
                        <!-- Receipt Number -->
                        <StackPanel Grid.Row="0" Grid.Column="0">
                            <TextBlock Text="رقم الوصل *" FontWeight="Medium" Margin="0,0,0,6"/>
                            <TextBox x:Name="ReceiptNumberTextBox"
                                   Style="{StaticResource MaterialDesignOutlinedTextBox}"
                                   materialDesign:HintAssist.Hint="أدخل رقم الوصل"
                                   Margin="0,0,0,12"/>
                        </StackPanel>

                        <!-- Payment Date -->
                        <StackPanel Grid.Row="1" Grid.Column="0">
                            <TextBlock Text="تاريخ الدفع *" FontWeight="Medium" Margin="0,0,0,6"/>
                            <DatePicker x:Name="PaymentDatePicker"
                                      Style="{StaticResource MaterialDesignOutlinedDatePicker}"
                                      materialDesign:HintAssist.Hint="اختر تاريخ الدفع"
                                      Margin="0,0,0,12"/>
                        </StackPanel>

                        <!-- Right Column -->
                        <!-- Payment Status -->
                        <StackPanel Grid.Row="0" Grid.Column="2">
                            <TextBlock Text="حالة التسديد *" FontWeight="Medium" Margin="0,0,0,6"/>
                            <ComboBox x:Name="PaymentStatusComboBox"
                                    Style="{StaticResource MaterialDesignOutlinedComboBox}"
                                    materialDesign:HintAssist.Hint="اختر حالة التسديد"
                                    SelectionChanged="PaymentStatusComboBox_SelectionChanged"
                                    Margin="0,0,0,12">
                                <ComboBoxItem Content="تسديد كامل" Tag="FullPayment"/>
                                <ComboBoxItem Content="تسديد جزئي" Tag="PartialPayment"/>
                                <ComboBoxItem Content="تسديد مع خصم" Tag="PaymentWithDiscount"/>
                                <ComboBoxItem Content="تسديد واسترجاع المتبقي" Tag="PaymentWithRefund"/>
                            </ComboBox>
                        </StackPanel>

                        <!-- Payment Method -->
                        <StackPanel Grid.Row="1" Grid.Column="2">
                            <TextBlock Text="طريقة الدفع *" FontWeight="Medium" Margin="0,0,0,6"/>
                            <ComboBox x:Name="PaymentMethodComboBox"
                                    Style="{StaticResource MaterialDesignOutlinedComboBox}"
                                    materialDesign:HintAssist.Hint="اختر طريقة الدفع"
                                    Margin="0,0,0,12">
                                <ComboBoxItem Content="نقدي" Tag="Cash"/>
                                <ComboBoxItem Content="بطاقة" Tag="CreditCard"/>
                            </ComboBox>
                        </StackPanel>
                    </Grid>

                    <!-- Amount Section -->
                    <Grid Margin="0,0,0,12">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="15"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>

                        <!-- Amount -->
                        <StackPanel Grid.Column="0">
                            <TextBlock Text="المبلغ (دينار عراقي) *" FontWeight="Medium" Margin="0,0,0,6"/>
                            <TextBox x:Name="AmountTextBox"
                                   Style="{StaticResource MaterialDesignOutlinedTextBox}"
                                   materialDesign:HintAssist.Hint="أدخل المبلغ"
                                   TextChanged="AmountTextBox_TextChanged"
                                   Margin="0,0,0,0"/>
                        </StackPanel>

                        <!-- Discount Amount (visible only when PaymentWithDiscount is selected) -->
                        <StackPanel Grid.Column="2" x:Name="DiscountPanel" Visibility="Collapsed">
                            <TextBlock x:Name="DiscountLabel" Text="مبلغ الخصم (دينار عراقي)" FontWeight="Medium" Margin="0,0,0,6"/>
                            <TextBox x:Name="DiscountAmountTextBox"
                                   Style="{StaticResource MaterialDesignOutlinedTextBox}"
                                   materialDesign:HintAssist.Hint="أدخل مبلغ الخصم"
                                   TextChanged="DiscountAmountTextBox_TextChanged"
                                   Margin="0,0,0,0"/>
                        </StackPanel>
                    </Grid>

                    <!-- Refund Value Display (visible only when PaymentWithRefund is selected) -->
                    <StackPanel x:Name="RefundPanel" Visibility="Collapsed" Margin="0,0,0,12">
                        <TextBlock x:Name="RefundValueLabel" Text="قيمة البضاعة المسترجعة:" FontWeight="Medium" Margin="0,0,0,6"/>
                        <Border x:Name="RefundValueDisplay"
                                Background="#F5F5F5"
                                BorderBrush="#E0E0E0"
                                BorderThickness="1"
                                CornerRadius="4"
                                Padding="10">
                            <TextBlock x:Name="RefundValueText"
                                     Text="0 د.ع"
                                     FontSize="13"
                                     FontWeight="SemiBold"
                                     Foreground="#9C27B0"
                                     VerticalAlignment="Center"/>
                        </Border>
                    </StackPanel>
                    
                    <!-- Notes and Attachment Section -->
                    <Grid Margin="0,0,0,12">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="15"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>

                        <!-- Notes -->
                        <StackPanel Grid.Column="0">
                            <TextBlock Text="الملاحظات" FontWeight="Medium" Margin="0,0,0,6"/>
                            <TextBox x:Name="NotesTextBox"
                                   Style="{StaticResource MaterialDesignOutlinedTextBox}"
                                   materialDesign:HintAssist.Hint="أدخل ملاحظات إضافية (اختياري)"
                                   Height="60"
                                   TextWrapping="Wrap"
                                   AcceptsReturn="True"
                                   VerticalScrollBarVisibility="Auto"/>
                        </StackPanel>

                        <!-- Receipt Attachment -->
                        <StackPanel Grid.Column="2">
                            <TextBlock Text="مرفق الوصل" FontWeight="Medium" Margin="0,0,0,6"/>
                            <Grid>
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                </Grid.RowDefinitions>

                                <TextBox x:Name="AttachmentPathTextBox"
                                       Grid.Row="0"
                                       Style="{StaticResource MaterialDesignOutlinedTextBox}"
                                       materialDesign:HintAssist.Hint="اسم الملف المرفق"
                                       IsReadOnly="True"
                                       Margin="0,0,0,6"/>

                                <StackPanel Grid.Row="1" Orientation="Horizontal" HorizontalAlignment="Center">
                                    <Button Style="{StaticResource MaterialDesignIconButton}"
                                          Click="BrowseAttachmentButton_Click"
                                          ToolTip="تصفح الملفات"
                                          Width="32" Height="32" Margin="2">
                                        <materialDesign:PackIcon Kind="FolderOpen" Width="16" Height="16"/>
                                    </Button>

                                    <Button x:Name="PreviewAttachmentButton"
                                          Style="{StaticResource MaterialDesignIconButton}"
                                          Click="PreviewAttachmentButton_Click"
                                          ToolTip="معاينة المرفق"
                                          Visibility="Collapsed"
                                          Width="32" Height="32" Margin="2">
                                        <materialDesign:PackIcon Kind="Eye" Width="16" Height="16" Foreground="#007BFF"/>
                                    </Button>

                                    <Button Style="{StaticResource MaterialDesignIconButton}"
                                          Click="RemoveAttachmentButton_Click"
                                          ToolTip="إزالة المرفق"
                                          Width="32" Height="32" Margin="2">
                                        <materialDesign:PackIcon Kind="Close" Width="16" Height="16" Foreground="#DC3545"/>
                                    </Button>
                                </StackPanel>
                            </Grid>
                        </StackPanel>
                    </Grid>
                </StackPanel>
            </materialDesign:Card>
        </ScrollViewer>

        <!-- Action Buttons -->
        <Border Grid.Row="2" Background="{DynamicResource MaterialDesignCardBackground}" Padding="16">
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                <Button x:Name="SaveButton"
                      Style="{StaticResource MaterialDesignRaisedButton}"
                      Click="SaveButton_Click"
                      MinWidth="100"
                      Margin="0,0,8,0">
                    <StackPanel Orientation="Horizontal">
                        <materialDesign:PackIcon Kind="ContentSave" Width="16" Height="16" Margin="0,0,4,0"/>
                        <TextBlock Text="حفظ"/>
                    </StackPanel>
                </Button>

                <Button x:Name="CancelButton"
                      Style="{StaticResource MaterialDesignOutlinedButton}"
                      Click="CancelButton_Click"
                      MinWidth="100"
                      Margin="8,0,0,0">
                    <StackPanel Orientation="Horizontal">
                        <materialDesign:PackIcon Kind="Cancel" Width="16" Height="16" Margin="0,0,4,0"/>
                        <TextBlock Text="إلغاء"/>
                    </StackPanel>
                </Button>
            </StackPanel>
        </Border>
    </Grid>
</Window>
