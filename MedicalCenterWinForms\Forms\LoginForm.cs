#nullable disable
#pragma warning disable CS0414
using System;
using System.Drawing;
using System.Drawing.Drawing2D;
using System.Threading.Tasks;
using System.Windows.Forms;
using MedicalCenterWinForms.Helpers;
using MedicalCenterWinForms.Services;
using MedicalCenterWinForms.Models;
using MedicalCenterWinForms.Animations;
using MedicalCenterWinForms.Icons;

namespace MedicalCenterWinForms.Forms
{
    /// <summary>
    /// Ultra Modern Medical Login Form with Material Design
    /// Features: Gradient backgrounds, smooth animations, modern UI elements
    /// </summary>
    public partial class LoginForm : Form
    {
        #region Fields and Properties
        
        private readonly DatabaseService _databaseService;
        private UserAccount? _currentUser;
        private bool _isLoading = false;
        private bool _passwordVisible = false;
        private bool _rememberMe = false;
        private bool _isDragging = false;
        private Point _dragStartPoint;
        
        // Animation and visual effects
        private System.Windows.Forms.Timer? _loadingTimer;
        private System.Windows.Forms.Timer? _pulseTimer;
        private float _loadingAngle = 0f;
        private float _pulseScale = 1.0f;
        private bool _pulseDirection = true;
        
        // Modern visual properties
        private readonly int _cornerRadius = 20;
        private readonly int _shadowSize = 15;
        private readonly Color _shadowColor = Color.FromArgb(30, 0, 0, 0);
        
        public UserAccount? CurrentUser => _currentUser;
        public bool LoginSuccessful { get; private set; } = false;
        
        #endregion

        #region Constructor and Initialization
        
        public LoginForm(DatabaseService databaseService)
        {
            _databaseService = databaseService ?? throw new ArgumentNullException(nameof(databaseService));
            
            InitializeComponent();
            InitializeModernUI();
            SetupEventHandlers();
            LoadSavedCredentials();
            
            // Enable animations if supported
            if (ModernAnimations.Config.EnableAnimations)
            {
                SetupAnimations();
            }
        }
        
        private void InitializeModernUI()
        {
            // Form properties for modern look
            this.FormBorderStyle = FormBorderStyle.None;
            this.StartPosition = FormStartPosition.CenterScreen;
            this.Size = new Size(1200, 800);
            this.BackColor = MaterialDesignHelper.Colors.Background;
            this.Font = ArabicFontHelper.GetArabicFont(10F);

            // Enable double buffering for smooth animations
            this.SetStyle(ControlStyles.AllPaintingInWmPaint |
                         ControlStyles.UserPaint |
                         ControlStyles.DoubleBuffer |
                         ControlStyles.ResizeRedraw, true);

            // Enhanced RTL support for Arabic
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;

            // Set culture for proper Arabic text rendering
            System.Threading.Thread.CurrentThread.CurrentUICulture = new System.Globalization.CultureInfo("ar-SA");
            System.Threading.Thread.CurrentThread.CurrentCulture = new System.Globalization.CultureInfo("ar-SA");
            
            // Window properties
            this.ShowInTaskbar = true;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.Text = "تسجيل الدخول - نظام إدارة المركز الطبي";
            
            // Set icon if available
            try
            {
                this.Icon = ModernIcons.GetApplicationIcon();
            }
            catch
            {
                // Ignore if icon not available
            }
        }
        
        private void SetupEventHandlers()
        {
            // Form events
            this.Load += LoginForm_Load;
            this.Paint += LoginForm_Paint;
            this.MouseDown += LoginForm_MouseDown;
            this.MouseMove += LoginForm_MouseMove;
            this.MouseUp += LoginForm_MouseUp;
            this.KeyDown += LoginForm_KeyDown;
            
            // Focus events for modern input styling
            this.Activated += (s, e) => this.Invalidate();
            this.Deactivate += (s, e) => this.Invalidate();
        }
        
        private void SetupAnimations()
        {
            // Loading animation timer
            _loadingTimer = new System.Windows.Forms.Timer { Interval = 50 };
            _loadingTimer.Tick += (s, e) =>
            {
                _loadingAngle += 10f;
                if (_loadingAngle >= 360f) _loadingAngle = 0f;
                
                if (_isLoading)
                {
                    // Invalidate only the loading area to improve performance
                    var loadingRect = GetLoadingButtonRect();
                    this.Invalidate(loadingRect);
                }
            };
            
            // Pulse animation timer for interactive elements
            _pulseTimer = new System.Windows.Forms.Timer { Interval = 30 };
            _pulseTimer.Tick += (s, e) =>
            {
                if (_pulseDirection)
                {
                    _pulseScale += 0.02f;
                    if (_pulseScale >= 1.1f) _pulseDirection = false;
                }
                else
                {
                    _pulseScale -= 0.02f;
                    if (_pulseScale <= 1.0f) _pulseDirection = true;
                }
                
                // Invalidate button areas that need pulse effect
                InvalidatePulseAreas();
            };
        }
        
        #endregion

        #region Event Handlers
        
        private async void LoginForm_Load(object sender, EventArgs e)
        {
            // Apply modern theme
            ApplyModernTheme();
            
            // Start entrance animation if enabled
            if (ModernAnimations.Config.EnableAnimations)
            {
                await PlayEntranceAnimation();
            }
            
            // Focus on username field
            FocusUsernameField();
        }
        
        private void LoginForm_KeyDown(object sender, KeyEventArgs e)
        {
            switch (e.KeyCode)
            {
                case Keys.Enter:
                    if (!_isLoading)
                    {
                        _ = PerformLoginAsync();
                    }
                    e.Handled = true;
                    break;
                    
                case Keys.Escape:
                    this.Close();
                    e.Handled = true;
                    break;
                    
                case Keys.F1:
                    ShowHelpDialog();
                    e.Handled = true;
                    break;
                    
                case Keys.Tab:
                    HandleTabNavigation(e.Shift);
                    e.Handled = true;
                    break;
            }
        }
        
        #endregion

        #region Drag and Drop Functionality

        private void LoginForm_MouseDown(object sender, MouseEventArgs e)
        {
            if (e.Button == MouseButtons.Left)
            {
                _isDragging = true;
                _dragStartPoint = e.Location;
                this.Cursor = Cursors.SizeAll;
            }
        }

        private void LoginForm_MouseMove(object sender, MouseEventArgs e)
        {
            if (_isDragging && e.Button == MouseButtons.Left)
            {
                Point currentScreenPos = PointToScreen(e.Location);
                Point newLocation = new Point(
                    currentScreenPos.X - _dragStartPoint.X,
                    currentScreenPos.Y - _dragStartPoint.Y);
                this.Location = newLocation;
            }
        }

        private void LoginForm_MouseUp(object sender, MouseEventArgs e)
        {
            if (_isDragging)
            {
                _isDragging = false;
                this.Cursor = Cursors.Default;
            }
        }

        #endregion

        #region Custom Painting and Visual Effects

        private void LoginForm_Paint(object sender, PaintEventArgs e)
        {
            Graphics g = e.Graphics;
            g.SmoothingMode = SmoothingMode.AntiAlias;
            g.TextRenderingHint = System.Drawing.Text.TextRenderingHint.ClearTypeGridFit;

            // Draw gradient background for left panel
            DrawGradientBackground(g);

            // Draw login card shadow and rounded corners
            DrawLoginCardShadow(g);

            // Draw decorative elements
            DrawDecorativeElements(g);

            // Draw loading animation if active
            if (_isLoading)
            {
                DrawLoadingAnimation(g);
            }
        }

        private void DrawGradientBackground(Graphics g)
        {
            Rectangle leftPanelRect = new Rectangle(0, 0, 600, this.Height);

            using (LinearGradientBrush brush = new LinearGradientBrush(
                leftPanelRect,
                MaterialDesignHelper.Colors.Primary,
                MaterialDesignHelper.Colors.PrimaryDark,
                LinearGradientMode.Vertical))
            {
                // Add color blend for more sophisticated gradient
                ColorBlend blend = new ColorBlend();
                blend.Colors = new Color[] {
                    MaterialDesignHelper.Colors.Primary,
                    MaterialDesignHelper.Colors.PrimaryAccent,
                    MaterialDesignHelper.Colors.PrimaryDark
                };
                blend.Positions = new float[] { 0.0f, 0.5f, 1.0f };
                brush.InterpolationColors = blend;

                g.FillRectangle(brush, leftPanelRect);
            }

            // Add geometric pattern overlay
            DrawGeometricPattern(g, leftPanelRect);
        }

        private void DrawGeometricPattern(Graphics g, Rectangle bounds)
        {
            using (Pen pen = new Pen(Color.FromArgb(20, 255, 255, 255), 1))
            {
                // Draw subtle geometric lines
                for (int i = 0; i < bounds.Width; i += 100)
                {
                    g.DrawLine(pen, i, 0, i + 200, bounds.Height);
                }

                for (int i = 0; i < bounds.Height; i += 100)
                {
                    g.DrawLine(pen, 0, i, bounds.Width, i + 150);
                }
            }
        }

        private void DrawLoginCardShadow(Graphics g)
        {
            Rectangle cardRect = new Rectangle(675, 100, 450, 600);
            Rectangle shadowRect = new Rectangle(
                cardRect.X + _shadowSize,
                cardRect.Y + _shadowSize,
                cardRect.Width,
                cardRect.Height);

            // Draw shadow
            using (GraphicsPath shadowPath = GetRoundedRectanglePath(shadowRect, _cornerRadius))
            using (PathGradientBrush shadowBrush = new PathGradientBrush(shadowPath))
            {
                shadowBrush.CenterColor = _shadowColor;
                shadowBrush.SurroundColors = new Color[] { Color.Transparent };
                g.FillPath(shadowBrush, shadowPath);
            }

            // Draw card background
            using (GraphicsPath cardPath = GetRoundedRectanglePath(cardRect, _cornerRadius))
            using (SolidBrush cardBrush = new SolidBrush(MaterialDesignHelper.Colors.Surface))
            {
                g.FillPath(cardBrush, cardPath);
            }
        }

        private void DrawDecorativeElements(Graphics g)
        {
            // Draw medical cross icon in logo area
            Rectangle logoRect = new Rectangle(240, 150, 120, 120);
            DrawMedicalCross(g, logoRect);

            // Draw pulse line animation
            if (ModernAnimations.Config.EnableAnimations)
            {
                DrawPulseLine(g);
            }
        }

        private void DrawMedicalCross(Graphics g, Rectangle bounds)
        {
            using (SolidBrush brush = new SolidBrush(MaterialDesignHelper.Colors.TextOnPrimary))
            {
                // Vertical bar
                Rectangle verticalBar = new Rectangle(
                    bounds.X + bounds.Width / 2 - 8,
                    bounds.Y + 20,
                    16,
                    bounds.Height - 40);
                g.FillRectangle(brush, verticalBar);

                // Horizontal bar
                Rectangle horizontalBar = new Rectangle(
                    bounds.X + 20,
                    bounds.Y + bounds.Height / 2 - 8,
                    bounds.Width - 40,
                    16);
                g.FillRectangle(brush, horizontalBar);
            }
        }

        private void DrawPulseLine(Graphics g)
        {
            using (Pen pen = new Pen(MaterialDesignHelper.Colors.AccentLight, 2))
            {
                // Draw animated pulse line
                int y = 450;
                for (int x = 50; x < 550; x += 20)
                {
                    int height = (int)(Math.Sin((x + _loadingAngle) * 0.1) * 10);
                    g.DrawLine(pen, x, y, x + 10, y + height);
                }
            }
        }

        private void DrawLoadingAnimation(Graphics g)
        {
            Rectangle loadingRect = GetLoadingButtonRect();
            Point center = new Point(
                loadingRect.X + loadingRect.Width / 2,
                loadingRect.Y + loadingRect.Height / 2);

            using (Pen pen = new Pen(MaterialDesignHelper.Colors.TextOnPrimary, 3))
            {
                // Draw spinning circle
                g.DrawArc(pen, center.X - 15, center.Y - 15, 30, 30, _loadingAngle, 90);
            }
        }

        private GraphicsPath GetRoundedRectanglePath(Rectangle rect, int radius)
        {
            GraphicsPath path = new GraphicsPath();

            path.AddArc(rect.X, rect.Y, radius * 2, radius * 2, 180, 90);
            path.AddArc(rect.Right - radius * 2, rect.Y, radius * 2, radius * 2, 270, 90);
            path.AddArc(rect.Right - radius * 2, rect.Bottom - radius * 2, radius * 2, radius * 2, 0, 90);
            path.AddArc(rect.X, rect.Bottom - radius * 2, radius * 2, radius * 2, 90, 90);
            path.CloseFigure();

            return path;
        }

        #endregion

        #region Helper Methods Implementation

        private Rectangle GetLoadingButtonRect()
        {
            return new Rectangle(725, 500, 350, 55);
        }

        private void InvalidatePulseAreas()
        {
            // Invalidate button areas that need pulse effect
            Rectangle loginButtonRect = new Rectangle(725, 500, 350, 55);
            this.Invalidate(loginButtonRect);
        }

        private void ApplyModernTheme()
        {
            // Apply modern theme to all controls
            ApplyThemeToControls(this.Controls);
        }

        private void ApplyThemeToControls(Control.ControlCollection controls)
        {
            foreach (Control control in controls)
            {
                // Apply Arabic RTL settings to all controls
                ApplyArabicSettings(control);

                if (control is TextBox textBox)
                {
                    ApplyTextBoxTheme(textBox);
                }
                else if (control is Button button)
                {
                    ApplyButtonTheme(button);
                }
                else if (control is Label label)
                {
                    ApplyLabelTheme(label);
                }
                else if (control is Panel panel)
                {
                    ApplyPanelTheme(panel);
                }

                // Recursively apply to child controls
                if (control.HasChildren)
                {
                    ApplyThemeToControls(control.Controls);
                }
            }
        }

        private void ApplyArabicSettings(Control control)
        {
            // Apply RTL settings to all controls that support text
            if (control is TextBox || control is Label || control is Button || control is CheckBox)
            {
                control.RightToLeft = RightToLeft.Yes;

                // Set appropriate font for Arabic text
                if (control.Font.Name != "Segoe UI Emoji")
                {
                    control.Font = ArabicFontHelper.GetArabicFont(control.Font.Size, control.Font.Style);
                }
            }
        }

        private void ApplyTextBoxTheme(TextBox textBox)
        {
            textBox.BackColor = MaterialDesignHelper.Colors.SurfaceElevated;
            textBox.ForeColor = MaterialDesignHelper.Colors.TextPrimary;
            textBox.BorderStyle = BorderStyle.None;
            textBox.RightToLeft = RightToLeft.Yes;
            textBox.TextAlign = HorizontalAlignment.Right;

            // Add custom border painting
            textBox.Paint += (s, e) =>
            {
                if (textBox.Focused)
                {
                    using (Pen pen = new Pen(MaterialDesignHelper.Colors.Primary, 2))
                    {
                        e.Graphics.DrawRectangle(pen, 0, 0, textBox.Width - 1, textBox.Height - 1);
                    }
                }
                else
                {
                    using (Pen pen = new Pen(MaterialDesignHelper.Colors.TextHint, 1))
                    {
                        e.Graphics.DrawRectangle(pen, 0, 0, textBox.Width - 1, textBox.Height - 1);
                    }
                }
            };
        }

        private void ApplyButtonTheme(Button button)
        {
            button.FlatStyle = FlatStyle.Flat;
            button.FlatAppearance.BorderSize = 0;
            button.Cursor = Cursors.Hand;
            button.RightToLeft = RightToLeft.Yes;
            button.TextAlign = ContentAlignment.MiddleCenter;

            // Add hover effects
            button.MouseEnter += (s, e) =>
            {
                if (button == loginButton)
                {
                    button.BackColor = MaterialDesignHelper.Colors.PrimaryDark;
                }
                else
                {
                    button.BackColor = MaterialDesignHelper.Colors.SurfaceElevated;
                }
            };

            button.MouseLeave += (s, e) =>
            {
                if (button == loginButton)
                {
                    button.BackColor = MaterialDesignHelper.Colors.Primary;
                }
                else
                {
                    button.BackColor = Color.Transparent;
                }
            };
        }

        private void ApplyLabelTheme(Label label)
        {
            // Labels are already themed in Designer
        }

        private void ApplyPanelTheme(Panel panel)
        {
            if (panel == loginCard)
            {
                panel.BackColor = MaterialDesignHelper.Colors.Surface;

                // Add custom painting for rounded corners
                panel.Paint += (s, e) =>
                {
                    using (GraphicsPath path = GetRoundedRectanglePath(panel.ClientRectangle, _cornerRadius))
                    using (SolidBrush brush = new SolidBrush(MaterialDesignHelper.Colors.Surface))
                    {
                        e.Graphics.SmoothingMode = SmoothingMode.AntiAlias;
                        e.Graphics.FillPath(brush, path);
                    }
                };
            }
        }

        private async Task PlayEntranceAnimation()
        {
            if (!ModernAnimations.Config.EnableAnimations) return;

            // Fade in animation
            this.Opacity = 0;
            this.Show();

            for (int i = 0; i <= 100; i += 5)
            {
                this.Opacity = i / 100.0;
                await Task.Delay(20);
            }

            // Slide in login card
            if (loginCard != null)
            {
                Point originalLocation = loginCard.Location;
                loginCard.Location = new Point(originalLocation.X, originalLocation.Y + 100);

                for (int i = 0; i <= 20; i++)
                {
                    loginCard.Location = new Point(
                        originalLocation.X,
                        originalLocation.Y + 100 - (i * 5));
                    await Task.Delay(15);
                }
            }
        }

        private void FocusUsernameField()
        {
            if (usernameTextBox != null)
            {
                usernameTextBox.Focus();
                usernameTextBox.SelectAll();
            }
        }

        private async Task PerformLoginAsync()
        {
            if (_isLoading) return;

            string username = usernameTextBox?.Text?.Trim() ?? "";
            string password = passwordTextBox?.Text ?? "";

            // Validate input
            if (string.IsNullOrEmpty(username))
            {
                ShowStatus("يرجى إدخال اسم المستخدم", true);
                usernameTextBox?.Focus();
                return;
            }

            if (string.IsNullOrEmpty(password))
            {
                ShowStatus("يرجى إدخال كلمة المرور", true);
                passwordTextBox?.Focus();
                return;
            }

            // Start loading state
            SetLoadingState(true);

            try
            {
                // Simulate network delay for better UX
                await Task.Delay(1000);

                // Attempt login
                var user = await _databaseService.AuthenticateUserAsync(username, password);

                if (user != null)
                {
                    _currentUser = user;
                    LoginSuccessful = true;

                    // Update last login date
                    user.LastLoginDate = DateTime.Now;
                    await _databaseService.UpdateUserAsync(user);

                    // Save credentials if remember me is checked
                    if (_rememberMe)
                    {
                        SaveCredentials(username);
                    }

                    ShowStatus("تم تسجيل الدخول بنجاح", false);

                    // Play success animation
                    if (ModernAnimations.Config.EnableAnimations)
                    {
                        await PlaySuccessAnimation();
                    }

                    await Task.Delay(500);
                    this.DialogResult = DialogResult.OK;
                    this.Close();
                }
                else
                {
                    ShowStatus("اسم المستخدم أو كلمة المرور غير صحيحة", true);
                    passwordTextBox?.Clear();
                    passwordTextBox?.Focus();

                    // Play error animation
                    if (ModernAnimations.Config.EnableAnimations)
                    {
                        await PlayErrorAnimation();
                    }
                }
            }
            catch (Exception ex)
            {
                ShowStatus($"خطأ في الاتصال: {ex.Message}", true);
            }
            finally
            {
                SetLoadingState(false);
            }
        }

        private void ShowHelpDialog()
        {
            string helpMessage = @"مساعدة تسجيل الدخول:

🔐 بيانات الاختبار الافتراضية:
• المدير: admin / admin123
• الاستقبال: reception / reception123
• الكاشير: cashier / cashier123

⌨️ اختصارات لوحة المفاتيح:
• Enter: تسجيل الدخول
• Escape: إغلاق النافذة
• F1: عرض هذه المساعدة
• Tab: التنقل بين الحقول

🎨 الميزات:
• تصميم حديث مع Material Design
• دعم كامل للغة العربية
• رسوم متحركة ناعمة
• إمكانية سحب النافذة

للمساعدة الإضافية، يرجى التواصل مع مدير النظام.";

            MessageBox.Show(helpMessage, "المساعدة - تسجيل الدخول",
                MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void HandleTabNavigation(bool reverse)
        {
            if (reverse)
            {
                // Shift+Tab - navigate backwards
                if (passwordTextBox?.Focused == true)
                {
                    usernameTextBox?.Focus();
                }
                else if (loginButton?.Focused == true)
                {
                    passwordTextBox?.Focus();
                }
                else
                {
                    loginButton?.Focus();
                }
            }
            else
            {
                // Tab - navigate forwards
                if (usernameTextBox?.Focused == true)
                {
                    passwordTextBox?.Focus();
                }
                else if (passwordTextBox?.Focused == true)
                {
                    loginButton?.Focus();
                }
                else
                {
                    usernameTextBox?.Focus();
                }
            }
        }

        private void LoadSavedCredentials()
        {
            try
            {
                // Load from application settings or registry
                string savedUsername = Properties.Settings.Default.SavedUsername ?? "";
                bool rememberMe = Properties.Settings.Default.RememberMe;

                if (!string.IsNullOrEmpty(savedUsername) && rememberMe)
                {
                    if (usernameTextBox != null)
                    {
                        usernameTextBox.Text = savedUsername;
                    }

                    if (rememberMeCheckBox != null)
                    {
                        rememberMeCheckBox.Checked = true;
                        _rememberMe = true;
                    }
                }
            }
            catch
            {
                // Ignore errors when loading saved credentials
            }
        }

        #endregion

        #region UI State Management and Animations

        private void SetLoadingState(bool loading)
        {
            _isLoading = loading;

            if (loginButton != null && loadingPanel != null)
            {
                loginButton.Visible = !loading;
                loadingPanel.Visible = loading;
            }

            if (usernameTextBox != null) usernameTextBox.Enabled = !loading;
            if (passwordTextBox != null) passwordTextBox.Enabled = !loading;
            if (rememberMeCheckBox != null) rememberMeCheckBox.Enabled = !loading;

            if (loading)
            {
                _loadingTimer?.Start();
                this.Cursor = Cursors.WaitCursor;
            }
            else
            {
                _loadingTimer?.Stop();
                this.Cursor = Cursors.Default;
            }
        }

        private void ShowStatus(string message, bool isError)
        {
            if (statusLabel != null)
            {
                statusLabel.Text = message;
                statusLabel.ForeColor = isError
                    ? MaterialDesignHelper.Colors.Error
                    : MaterialDesignHelper.Colors.Success;

                // Auto-hide status after 5 seconds
                System.Windows.Forms.Timer hideTimer = new System.Windows.Forms.Timer { Interval = 5000 };
                hideTimer.Tick += (s, e) =>
                {
                    statusLabel.Text = "";
                    hideTimer.Stop();
                    hideTimer.Dispose();
                };
                hideTimer.Start();
            }
        }

        private async Task PlaySuccessAnimation()
        {
            if (loginCard == null) return;

            // Green pulse effect
            Color originalColor = loginCard.BackColor;

            for (int i = 0; i < 3; i++)
            {
                loginCard.BackColor = MaterialDesignHelper.Colors.SuccessLight;
                await Task.Delay(150);
                loginCard.BackColor = originalColor;
                await Task.Delay(150);
            }
        }

        private async Task PlayErrorAnimation()
        {
            if (loginCard == null) return;

            // Shake animation
            Point originalLocation = loginCard.Location;

            for (int i = 0; i < 6; i++)
            {
                loginCard.Location = new Point(originalLocation.X + (i % 2 == 0 ? 5 : -5), originalLocation.Y);
                await Task.Delay(50);
            }

            loginCard.Location = originalLocation;
        }

        private void SaveCredentials(string username)
        {
            try
            {
                Properties.Settings.Default.SavedUsername = username;
                Properties.Settings.Default.RememberMe = _rememberMe;
                Properties.Settings.Default.Save();
            }
            catch
            {
                // Ignore errors when saving credentials
            }
        }

        #endregion

        #region Event Handlers for Controls

        protected override void OnLoad(EventArgs e)
        {
            base.OnLoad(e);

            // Setup control event handlers
            SetupControlEventHandlers();
        }

        private void SetupControlEventHandlers()
        {
            // Password toggle button
            if (passwordToggleButton != null)
            {
                passwordToggleButton.Click += PasswordToggleButton_Click;
            }

            // Remember me checkbox
            if (rememberMeCheckBox != null)
            {
                rememberMeCheckBox.CheckedChanged += RememberMeCheckBox_CheckedChanged;
            }

            // Login button
            if (loginButton != null)
            {
                loginButton.Click += async (s, e) => await PerformLoginAsync();
            }

            // Help button
            if (helpButton != null)
            {
                helpButton.Click += (s, e) => ShowHelpDialog();
            }

            // Settings button
            if (settingsButton != null)
            {
                settingsButton.Click += SettingsButton_Click;
            }

            // Close button
            if (closeButton != null)
            {
                closeButton.Click += (s, e) => this.Close();
            }

            // Minimize button
            if (minimizeButton != null)
            {
                minimizeButton.Click += (s, e) => this.WindowState = FormWindowState.Minimized;
            }

            // Text box events
            if (usernameTextBox != null)
            {
                usernameTextBox.KeyDown += TextBox_KeyDown;
                usernameTextBox.Enter += (s, e) => this.Invalidate();
                usernameTextBox.Leave += (s, e) => this.Invalidate();
            }

            if (passwordTextBox != null)
            {
                passwordTextBox.KeyDown += TextBox_KeyDown;
                passwordTextBox.Enter += (s, e) => this.Invalidate();
                passwordTextBox.Leave += (s, e) => this.Invalidate();
            }
        }

        private void PasswordToggleButton_Click(object sender, EventArgs e)
        {
            if (passwordTextBox != null && passwordToggleButton != null)
            {
                _passwordVisible = !_passwordVisible;
                passwordTextBox.UseSystemPasswordChar = !_passwordVisible;
                passwordToggleButton.Text = _passwordVisible ? "🙈" : "👁";
            }
        }

        private void RememberMeCheckBox_CheckedChanged(object sender, EventArgs e)
        {
            if (rememberMeCheckBox != null)
            {
                _rememberMe = rememberMeCheckBox.Checked;
            }
        }

        private void SettingsButton_Click(object sender, EventArgs e)
        {
            // Show settings dialog or menu
            MessageBox.Show("إعدادات النظام ستكون متاحة في الإصدار القادم", "الإعدادات",
                MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private async void TextBox_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Enter && !_isLoading)
            {
                await PerformLoginAsync();
                e.Handled = true;
            }
        }

        #endregion

        #region Cleanup and Disposal

        protected override void Dispose(bool disposing)
        {
            if (disposing)
            {
                // Dispose timers
                _loadingTimer?.Stop();
                _loadingTimer?.Dispose();
                _pulseTimer?.Stop();
                _pulseTimer?.Dispose();
            }

            base.Dispose(disposing);
        }

        #endregion
    }
}
