#nullable disable
using MedicalCenterWinForms.Helpers;

namespace MedicalCenterWinForms.Forms
{
    partial class LoginForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            
            // Main panels
            this.leftPanel = new Panel();
            this.rightPanel = new Panel();
            this.loginCard = new Panel();
            
            // Header elements
            this.logoPanel = new Panel();
            this.titleLabel = new Label();
            this.subtitleLabel = new Label();
            this.welcomeLabel = new Label();
            
            // Input controls
            this.usernamePanel = new Panel();
            this.usernameTextBox = new TextBox();
            this.usernameLabel = new Label();
            this.usernameIcon = new Label();
            
            this.passwordPanel = new Panel();
            this.passwordTextBox = new TextBox();
            this.passwordLabel = new Label();
            this.passwordIcon = new Label();
            this.passwordToggleButton = new Button();
            
            // Action controls
            this.rememberMeCheckBox = new CheckBox();
            this.loginButton = new Button();
            this.loadingPanel = new Panel();
            this.loadingLabel = new Label();
            
            // Footer controls
            this.helpButton = new Button();
            this.settingsButton = new Button();
            this.closeButton = new Button();
            this.minimizeButton = new Button();
            
            // Status and feedback
            this.statusLabel = new Label();
            this.versionLabel = new Label();
            
            this.SuspendLayout();
            
            // Configure main panels
            ConfigureMainPanels();
            
            // Configure header elements
            ConfigureHeaderElements();
            
            // Configure input controls
            ConfigureInputControls();
            
            // Configure action controls
            ConfigureActionControls();
            
            // Configure footer controls
            ConfigureFooterControls();
            
            // Configure status elements
            ConfigureStatusElements();
            
            // Add all controls to form
            AddControlsToForm();
            
            this.ResumeLayout(false);
            this.PerformLayout();
        }

        private void ConfigureMainPanels()
        {
            // Left Panel - Branding and Welcome
            this.leftPanel.Size = new Size(600, 800);
            this.leftPanel.Location = new Point(0, 0);
            this.leftPanel.BackColor = MaterialDesignHelper.Colors.Primary;
            this.leftPanel.Dock = DockStyle.Left;
            
            // Right Panel - Login Form
            this.rightPanel.Size = new Size(600, 800);
            this.rightPanel.Location = new Point(600, 0);
            this.rightPanel.BackColor = MaterialDesignHelper.Colors.Background;
            this.rightPanel.Dock = DockStyle.Fill;
            
            // Login Card - Floating card design
            this.loginCard.Size = new Size(450, 600);
            this.loginCard.Location = new Point(75, 100);
            this.loginCard.BackColor = MaterialDesignHelper.Colors.Surface;
            this.loginCard.Anchor = AnchorStyles.None;
        }

        private void ConfigureHeaderElements()
        {
            // Logo Panel
            this.logoPanel.Size = new Size(120, 120);
            this.logoPanel.Location = new Point(240, 150);
            this.logoPanel.BackColor = Color.Transparent;
            
            // Title Label
            this.titleLabel.Text = "نظام إدارة المركز الطبي";
            this.titleLabel.Font = ArabicFontHelper.GetArabicFont(28F, FontStyle.Bold);
            this.titleLabel.ForeColor = MaterialDesignHelper.Colors.TextOnPrimary;
            this.titleLabel.Size = new Size(500, 50);
            this.titleLabel.Location = new Point(50, 300);
            this.titleLabel.TextAlign = ContentAlignment.MiddleCenter;
            
            // Subtitle Label
            this.subtitleLabel.Text = "نظام متطور لإدارة العيادات والمراكز الطبية";
            this.subtitleLabel.Font = ArabicFontHelper.GetArabicFont(16F);
            this.subtitleLabel.ForeColor = MaterialDesignHelper.Colors.PrimaryLight;
            this.subtitleLabel.Size = new Size(500, 30);
            this.subtitleLabel.Location = new Point(50, 360);
            this.subtitleLabel.TextAlign = ContentAlignment.MiddleCenter;
            
            // Welcome Label
            this.welcomeLabel.Text = "مرحباً بك";
            this.welcomeLabel.Font = ArabicFontHelper.GetArabicFont(24F, FontStyle.Bold);
            this.welcomeLabel.ForeColor = MaterialDesignHelper.Colors.TextPrimary;
            this.welcomeLabel.Size = new Size(400, 40);
            this.welcomeLabel.Location = new Point(25, 50);
            this.welcomeLabel.TextAlign = ContentAlignment.MiddleCenter;
        }

        private void ConfigureInputControls()
        {
            // Username Panel
            this.usernamePanel.Size = new Size(350, 70);
            this.usernamePanel.Location = new Point(50, 150);
            this.usernamePanel.BackColor = Color.Transparent;
            
            // Username Icon
            this.usernameIcon.Text = "👤";
            this.usernameIcon.Font = new Font("Segoe UI Emoji", 16F);
            this.usernameIcon.Size = new Size(30, 30);
            this.usernameIcon.Location = new Point(310, 25);
            this.usernameIcon.TextAlign = ContentAlignment.MiddleCenter;
            this.usernameIcon.ForeColor = MaterialDesignHelper.Colors.Primary;
            
            // Username Label
            this.usernameLabel.Text = "اسم المستخدم";
            this.usernameLabel.Font = ArabicFontHelper.GetArabicFont(12F);
            this.usernameLabel.ForeColor = MaterialDesignHelper.Colors.TextSecondary;
            this.usernameLabel.Size = new Size(100, 20);
            this.usernameLabel.Location = new Point(240, 5);
            this.usernameLabel.TextAlign = ContentAlignment.MiddleRight;
            
            // Username TextBox
            this.usernameTextBox.Size = new Size(270, 35);
            this.usernameTextBox.Location = new Point(20, 25);
            this.usernameTextBox.Font = ArabicFontHelper.GetArabicFont(14F);
            this.usernameTextBox.BorderStyle = BorderStyle.None;
            this.usernameTextBox.BackColor = MaterialDesignHelper.Colors.SurfaceElevated;
            this.usernameTextBox.ForeColor = MaterialDesignHelper.Colors.TextPrimary;
            this.usernameTextBox.RightToLeft = RightToLeft.Yes;
            
            // Password Panel
            this.passwordPanel.Size = new Size(350, 70);
            this.passwordPanel.Location = new Point(50, 250);
            this.passwordPanel.BackColor = Color.Transparent;
            
            // Password Icon
            this.passwordIcon.Text = "🔒";
            this.passwordIcon.Font = new Font("Segoe UI Emoji", 16F);
            this.passwordIcon.Size = new Size(30, 30);
            this.passwordIcon.Location = new Point(310, 25);
            this.passwordIcon.TextAlign = ContentAlignment.MiddleCenter;
            this.passwordIcon.ForeColor = MaterialDesignHelper.Colors.Primary;
            
            // Password Label
            this.passwordLabel.Text = "كلمة المرور";
            this.passwordLabel.Font = ArabicFontHelper.GetArabicFont(12F);
            this.passwordLabel.ForeColor = MaterialDesignHelper.Colors.TextSecondary;
            this.passwordLabel.Size = new Size(100, 20);
            this.passwordLabel.Location = new Point(240, 5);
            this.passwordLabel.TextAlign = ContentAlignment.MiddleRight;
            
            // Password TextBox
            this.passwordTextBox.Size = new Size(230, 35);
            this.passwordTextBox.Location = new Point(20, 25);
            this.passwordTextBox.Font = ArabicFontHelper.GetArabicFont(14F);
            this.passwordTextBox.BorderStyle = BorderStyle.None;
            this.passwordTextBox.BackColor = MaterialDesignHelper.Colors.SurfaceElevated;
            this.passwordTextBox.ForeColor = MaterialDesignHelper.Colors.TextPrimary;
            this.passwordTextBox.UseSystemPasswordChar = true;
            this.passwordTextBox.RightToLeft = RightToLeft.Yes;
            
            // Password Toggle Button
            this.passwordToggleButton.Text = "👁";
            this.passwordToggleButton.Font = new Font("Segoe UI Emoji", 12F);
            this.passwordToggleButton.Size = new Size(35, 35);
            this.passwordToggleButton.Location = new Point(260, 25);
            this.passwordToggleButton.FlatStyle = FlatStyle.Flat;
            this.passwordToggleButton.FlatAppearance.BorderSize = 0;
            this.passwordToggleButton.BackColor = Color.Transparent;
            this.passwordToggleButton.ForeColor = MaterialDesignHelper.Colors.TextSecondary;
            this.passwordToggleButton.Cursor = Cursors.Hand;
        }

        private void ConfigureActionControls()
        {
            // Remember Me CheckBox
            this.rememberMeCheckBox.Text = "تذكرني";
            this.rememberMeCheckBox.Font = ArabicFontHelper.GetArabicFont(11F);
            this.rememberMeCheckBox.ForeColor = MaterialDesignHelper.Colors.TextSecondary;
            this.rememberMeCheckBox.Size = new Size(100, 25);
            this.rememberMeCheckBox.Location = new Point(300, 350);
            this.rememberMeCheckBox.RightToLeft = RightToLeft.Yes;
            this.rememberMeCheckBox.CheckAlign = ContentAlignment.MiddleRight;
            
            // Login Button
            this.loginButton.Text = "تسجيل الدخول";
            this.loginButton.Font = ArabicFontHelper.GetArabicFont(16F, FontStyle.Bold);
            this.loginButton.Size = new Size(350, 55);
            this.loginButton.Location = new Point(50, 400);
            this.loginButton.BackColor = MaterialDesignHelper.Colors.Primary;
            this.loginButton.ForeColor = MaterialDesignHelper.Colors.TextOnPrimary;
            this.loginButton.FlatStyle = FlatStyle.Flat;
            this.loginButton.FlatAppearance.BorderSize = 0;
            this.loginButton.Cursor = Cursors.Hand;
            this.loginButton.RightToLeft = RightToLeft.Yes;
            
            // Loading Panel
            this.loadingPanel.Size = new Size(350, 55);
            this.loadingPanel.Location = new Point(50, 400);
            this.loadingPanel.BackColor = MaterialDesignHelper.Colors.PrimaryLight;
            this.loadingPanel.Visible = false;
            
            // Loading Label
            this.loadingLabel.Text = "جاري التحقق...";
            this.loadingLabel.Font = ArabicFontHelper.GetArabicFont(14F);
            this.loadingLabel.ForeColor = MaterialDesignHelper.Colors.TextOnPrimary;
            this.loadingLabel.Size = new Size(350, 55);
            this.loadingLabel.Location = new Point(0, 0);
            this.loadingLabel.TextAlign = ContentAlignment.MiddleCenter;
        }

        private void ConfigureFooterControls()
        {
            // Help Button
            this.helpButton.Text = "المساعدة";
            this.helpButton.Font = ArabicFontHelper.GetArabicFont(11F);
            this.helpButton.Size = new Size(80, 35);
            this.helpButton.Location = new Point(50, 500);
            this.helpButton.BackColor = Color.Transparent;
            this.helpButton.ForeColor = MaterialDesignHelper.Colors.Primary;
            this.helpButton.FlatStyle = FlatStyle.Flat;
            this.helpButton.FlatAppearance.BorderSize = 1;
            this.helpButton.FlatAppearance.BorderColor = MaterialDesignHelper.Colors.Primary;
            this.helpButton.Cursor = Cursors.Hand;
            
            // Settings Button
            this.settingsButton.Text = "الإعدادات";
            this.settingsButton.Font = ArabicFontHelper.GetArabicFont(11F);
            this.settingsButton.Size = new Size(80, 35);
            this.settingsButton.Location = new Point(150, 500);
            this.settingsButton.BackColor = Color.Transparent;
            this.settingsButton.ForeColor = MaterialDesignHelper.Colors.TextSecondary;
            this.settingsButton.FlatStyle = FlatStyle.Flat;
            this.settingsButton.FlatAppearance.BorderSize = 1;
            this.settingsButton.FlatAppearance.BorderColor = MaterialDesignHelper.Colors.TextSecondary;
            this.settingsButton.Cursor = Cursors.Hand;
            
            // Close Button
            this.closeButton.Text = "✕";
            this.closeButton.Font = new Font("Segoe UI", 14F, FontStyle.Bold);
            this.closeButton.Size = new Size(40, 40);
            this.closeButton.Location = new Point(550, 10);
            this.closeButton.BackColor = Color.Transparent;
            this.closeButton.ForeColor = MaterialDesignHelper.Colors.TextSecondary;
            this.closeButton.FlatStyle = FlatStyle.Flat;
            this.closeButton.FlatAppearance.BorderSize = 0;
            this.closeButton.Cursor = Cursors.Hand;
            
            // Minimize Button
            this.minimizeButton.Text = "−";
            this.minimizeButton.Font = new Font("Segoe UI", 14F, FontStyle.Bold);
            this.minimizeButton.Size = new Size(40, 40);
            this.minimizeButton.Location = new Point(500, 10);
            this.minimizeButton.BackColor = Color.Transparent;
            this.minimizeButton.ForeColor = MaterialDesignHelper.Colors.TextSecondary;
            this.minimizeButton.FlatStyle = FlatStyle.Flat;
            this.minimizeButton.FlatAppearance.BorderSize = 0;
            this.minimizeButton.Cursor = Cursors.Hand;
        }

        private void ConfigureStatusElements()
        {
            // Status Label
            this.statusLabel.Text = "";
            this.statusLabel.Font = ArabicFontHelper.GetArabicFont(11F);
            this.statusLabel.ForeColor = MaterialDesignHelper.Colors.TextSecondary;
            this.statusLabel.Size = new Size(350, 25);
            this.statusLabel.Location = new Point(50, 470);
            this.statusLabel.TextAlign = ContentAlignment.MiddleCenter;
            this.statusLabel.RightToLeft = RightToLeft.Yes;
            
            // Version Label
            this.versionLabel.Text = "الإصدار 1.0.0 - 2025";
            this.versionLabel.Font = ArabicFontHelper.GetArabicFont(9F);
            this.versionLabel.ForeColor = MaterialDesignHelper.Colors.TextHint;
            this.versionLabel.Size = new Size(200, 20);
            this.versionLabel.Location = new Point(50, 750);
            this.versionLabel.TextAlign = ContentAlignment.MiddleLeft;
        }

        private void AddControlsToForm()
        {
            // Add to left panel
            this.leftPanel.Controls.Add(this.logoPanel);
            this.leftPanel.Controls.Add(this.titleLabel);
            this.leftPanel.Controls.Add(this.subtitleLabel);
            this.leftPanel.Controls.Add(this.versionLabel);
            
            // Add to username panel
            this.usernamePanel.Controls.Add(this.usernameIcon);
            this.usernamePanel.Controls.Add(this.usernameLabel);
            this.usernamePanel.Controls.Add(this.usernameTextBox);
            
            // Add to password panel
            this.passwordPanel.Controls.Add(this.passwordIcon);
            this.passwordPanel.Controls.Add(this.passwordLabel);
            this.passwordPanel.Controls.Add(this.passwordTextBox);
            this.passwordPanel.Controls.Add(this.passwordToggleButton);
            
            // Add to loading panel
            this.loadingPanel.Controls.Add(this.loadingLabel);
            
            // Add to login card
            this.loginCard.Controls.Add(this.welcomeLabel);
            this.loginCard.Controls.Add(this.usernamePanel);
            this.loginCard.Controls.Add(this.passwordPanel);
            this.loginCard.Controls.Add(this.rememberMeCheckBox);
            this.loginCard.Controls.Add(this.loginButton);
            this.loginCard.Controls.Add(this.loadingPanel);
            this.loginCard.Controls.Add(this.statusLabel);
            this.loginCard.Controls.Add(this.helpButton);
            this.loginCard.Controls.Add(this.settingsButton);
            
            // Add to right panel
            this.rightPanel.Controls.Add(this.loginCard);
            this.rightPanel.Controls.Add(this.closeButton);
            this.rightPanel.Controls.Add(this.minimizeButton);
            
            // Add main panels to form
            this.Controls.Add(this.leftPanel);
            this.Controls.Add(this.rightPanel);
        }

        #endregion

        #region Control Declarations

        private Panel leftPanel;
        private Panel rightPanel;
        private Panel loginCard;
        private Panel logoPanel;
        private Label titleLabel;
        private Label subtitleLabel;
        private Label welcomeLabel;
        private Panel usernamePanel;
        private TextBox usernameTextBox;
        private Label usernameLabel;
        private Label usernameIcon;
        private Panel passwordPanel;
        private TextBox passwordTextBox;
        private Label passwordLabel;
        private Label passwordIcon;
        private Button passwordToggleButton;
        private CheckBox rememberMeCheckBox;
        private Button loginButton;
        private Panel loadingPanel;
        private Label loadingLabel;
        private Button helpButton;
        private Button settingsButton;
        private Button closeButton;
        private Button minimizeButton;
        private Label statusLabel;
        private Label versionLabel;

        #endregion


    }
}
