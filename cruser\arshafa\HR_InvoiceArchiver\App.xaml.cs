using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;
using System.Globalization;
using System.Threading;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.EntityFrameworkCore;
using HR_InvoiceArchiver.Data;
using HR_InvoiceArchiver.Data.Repositories;
using HR_InvoiceArchiver.Services;
using HR_InvoiceArchiver.Pages;
using HR_InvoiceArchiver.Windows;
using HR_InvoiceArchiver.Utils;

namespace HR_InvoiceArchiver;

public partial class App : Application
{
    public static ServiceProvider ServiceProvider { get; private set; } = null!;

    protected override void OnStartup(StartupEventArgs e)
    {
        try
        {
            // Set Arabic culture and text rendering settings
            SetupArabicCulture();

            var services = new ServiceCollection();
            ConfigureServices(services);

            ServiceProvider = services.BuildServiceProvider();

            // Initialize database
            using (var scope = ServiceProvider.CreateScope())
            {
                var context = scope.ServiceProvider.GetRequiredService<DatabaseContext>();
                context.Database.EnsureCreated();

                // Seed data if needed
                SeedData.SeedDatabaseAsync(context).Wait();
            }

            // Initialize global exception handler
            var exceptionHandler = ServiceProvider.GetRequiredService<IGlobalExceptionHandler>();
            exceptionHandler.Initialize();

            base.OnStartup(e);

            // تجاوز نافذة تسجيل الدخول مؤقتاً وإظهار النافذة الرئيسية مباشرة
            try
            {
                var mainWindow = ServiceProvider.GetRequiredService<MainWindow>();
                mainWindow.Show();
            }
            catch (Exception mainWindowEx)
            {
                MessageBox.Show($"خطأ في إنشاء النافذة الرئيسية: {mainWindowEx.Message}\n\nسأحاول إنشاء نافذة بديلة...", "خطأ", MessageBoxButton.OK, MessageBoxImage.Warning);

                // إنشاء نافذة بديلة بسيطة في حالة فشل النافذة الرئيسية
                try
                {
                    var fallbackWindow = new Window
                    {
                        Title = "HR Invoice Archiver - Fallback Mode",
                        Width = 800,
                        Height = 600,
                        WindowStartupLocation = WindowStartupLocation.CenterScreen,
                        Content = new TextBlock
                        {
                            Text = $"النافذة الرئيسية لا تعمل حالياً\n\nالخطأ: {mainWindowEx.Message}\n\nيرجى التحقق من إعدادات Material Design",
                            FontSize = 16,
                            HorizontalAlignment = HorizontalAlignment.Center,
                            VerticalAlignment = VerticalAlignment.Center,
                            TextAlignment = TextAlignment.Center,
                            FlowDirection = FlowDirection.RightToLeft,
                            TextWrapping = TextWrapping.Wrap,
                            Margin = new Thickness(20)
                        }
                    };
                    fallbackWindow.Show();
                }
                catch (Exception fallbackEx)
                {
                    MessageBox.Show($"فشل في إنشاء النافذة البديلة: {fallbackEx.Message}", "خطأ حرج", MessageBoxButton.OK, MessageBoxImage.Error);
                    Shutdown();
                    return;
                }
            }
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في بدء التطبيق: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            Shutdown();
        }
    }

    private void SetupArabicCulture()
    {
        try
        {
            // Set Arabic culture for proper text rendering but keep English numbers
            var arabicCulture = new CultureInfo("ar-SA");

            // Override number format to use English digits
            arabicCulture.NumberFormat.DigitSubstitution = DigitShapes.NativeNational;
            arabicCulture.NumberFormat.NativeDigits = new string[] { "0", "1", "2", "3", "4", "5", "6", "7", "8", "9" };

            // Create a custom culture that uses English numbers
            var customCulture = (CultureInfo)arabicCulture.Clone();
            customCulture.NumberFormat.DigitSubstitution = DigitShapes.None;

            Thread.CurrentThread.CurrentCulture = customCulture;
            Thread.CurrentThread.CurrentUICulture = customCulture;
            CultureInfo.DefaultThreadCurrentCulture = customCulture;
            CultureInfo.DefaultThreadCurrentUICulture = customCulture;

            // Enable text rendering optimizations for WPF
            // These settings will be applied globally through XAML styles

            // Set global flow direction for RTL languages
            FrameworkElement.FlowDirectionProperty.OverrideMetadata(
                typeof(FrameworkElement),
                new FrameworkPropertyMetadata(FlowDirection.RightToLeft));
        }
        catch (Exception ex)
        {
            MessageBox.Show($"تحذير: فشل في إعداد الثقافة العربية: {ex.Message}", "تحذير", MessageBoxButton.OK, MessageBoxImage.Warning);
        }
    }

    private void ConfigureServices(IServiceCollection services)
    {
        // Logging
        services.AddLogging(builder =>
        {
            builder.AddConsole();
            builder.SetMinimumLevel(Microsoft.Extensions.Logging.LogLevel.Information);
        });

        // Database
        services.AddDbContext<DatabaseContext>();

        // Repositories
        services.AddScoped<IInvoiceRepository, InvoiceRepository>();
        services.AddScoped<ISupplierRepository, SupplierRepository>();
        services.AddScoped<IPaymentRepository, PaymentRepository>();

        // Services
        services.AddSingleton<INavigationService, NavigationService>();
        services.AddScoped<IInvoiceService, InvoiceService>();
        services.AddScoped<ISupplierService, SupplierService>();
        services.AddScoped<IPaymentService, PaymentService>();
        services.AddScoped<IToastService, ToastService>();
        services.AddScoped<IErrorHandlingService, ErrorHandlingService>();
        services.AddScoped<ISuccessNotificationService, SuccessNotificationService>();
        services.AddScoped<IDashboardService, DashboardService>();
        services.AddScoped<IValidationService, ValidationService>();
        services.AddSingleton<IGlobalExceptionHandler, GlobalExceptionHandler>();
        services.AddScoped<IRetryService, RetryService>();

        // Enhanced Services - الخدمات المحسنة
        services.AddSingleton<ILoggingService, LoggingService>();
        services.AddScoped<IEnhancedErrorHandlingService, EnhancedErrorHandlingService>();
        services.AddSingleton<IEnhancedPerformanceMonitoringService, EnhancedPerformanceMonitoringService>();
        services.AddSingleton<ISettingsService, SettingsService>();

        // Security Services - خدمات الأمان
        services.AddSingleton<IEncryptionService, EncryptionService>();
        services.AddSingleton<ISecurityService, SecurityService>();

        // Performance Services - خدمات الأداء
        services.AddSingleton<IPerformanceOptimizationService, PerformanceOptimizationService>();

        // Import/Export Services - خدمات التصدير والاستيراد
        services.AddSingleton<IImportExportService, ImportExportService>();

        // Backup/Restore Services - خدمات النسخ الاحتياطي والاستعادة
        services.AddSingleton<IBackupRestoreService, BackupRestoreService>();

        // Performance Services - خدمات تحسين الأداء
        services.AddSingleton<ICacheService, CacheService>();
        services.AddSingleton<IBackgroundTaskService, BackgroundTaskService>();
        services.AddHostedService<BackgroundTaskService>();

        // Cloud Services
        services.AddScoped<ICloudStorageService, GoogleDriveService>();
        services.AddScoped<CloudSyncService>();
        services.AddScoped<DatabaseMigrationService>();

        // Pages - فقط الصفحات الموجودة
        services.AddTransient<DashboardPage>();
        services.AddTransient<InvoicesPage>();
        services.AddTransient<SuppliersPage>();
        services.AddTransient<PaymentsPage>();
        services.AddTransient<SimplePaymentsPage>();
        services.AddTransient<ReportsPage>();
        services.AddTransient<SearchPage>();
        services.AddTransient<SettingsPage>();
        services.AddTransient<PerformanceOptimizationPage>();
        services.AddTransient<ImportExportPage>();
        services.AddTransient<BackupRestorePage>();
        services.AddTransient<OffersPage>();
        services.AddTransient<OfferRepository>();
        services.AddTransient<OfferService>();

        // Windows - فقط النوافذ الموجودة
        services.AddTransient<MainWindow>();
        services.AddTransient<AddEditInvoiceWindow>();
        services.AddTransient<AddEditPaymentWindow>();

        // Initialize attachment directories
        try
        {
            FileHelper.InitializeAttachmentDirectories();
        }
        catch (Exception ex)
        {
            MessageBox.Show($"تحذير: فشل في إنشاء مجلدات المرفقات: {ex.Message}", "تحذير", MessageBoxButton.OK, MessageBoxImage.Warning);
        }
    }

    protected override void OnExit(ExitEventArgs e)
    {
        ServiceProvider?.Dispose();
        base.OnExit(e);
    }
}