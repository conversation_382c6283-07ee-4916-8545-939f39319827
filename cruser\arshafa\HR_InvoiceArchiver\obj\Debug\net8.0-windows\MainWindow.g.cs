﻿#pragma checksum "..\..\..\MainWindow.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "0DD6048BA3D4F7163350601FEC0E62CA949C63C0"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using MaterialDesignThemes.Wpf;
using MaterialDesignThemes.Wpf.Converters;
using MaterialDesignThemes.Wpf.Transitions;
using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace HR_InvoiceArchiver {
    
    
    /// <summary>
    /// MainWindow
    /// </summary>
    public partial class MainWindow : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 258 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button CloseButton;
        
        #line default
        #line hidden
        
        
        #line 259 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button MaximizeButton;
        
        #line default
        #line hidden
        
        
        #line 260 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button MinimizeButton;
        
        #line default
        #line hidden
        
        
        #line 296 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button CloudStorageButton;
        
        #line default
        #line hidden
        
        
        #line 338 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button DashboardButton;
        
        #line default
        #line hidden
        
        
        #line 344 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button InvoicesButton;
        
        #line default
        #line hidden
        
        
        #line 350 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button PaymentsButton;
        
        #line default
        #line hidden
        
        
        #line 356 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button SuppliersButton;
        
        #line default
        #line hidden
        
        
        #line 362 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button SearchButton;
        
        #line default
        #line hidden
        
        
        #line 368 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ReportsButton;
        
        #line default
        #line hidden
        
        
        #line 391 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button SettingsButton;
        
        #line default
        #line hidden
        
        
        #line 397 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ImportExportButton;
        
        #line default
        #line hidden
        
        
        #line 403 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BackupRestoreButton;
        
        #line default
        #line hidden
        
        
        #line 409 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button PerformanceButton;
        
        #line default
        #line hidden
        
        
        #line 433 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button SidebarCloudButton;
        
        #line default
        #line hidden
        
        
        #line 453 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ContentPresenter MainContentPresenter;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/HR_InvoiceArchiver;component/mainwindow.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\MainWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            
            #line 248 "..\..\..\MainWindow.xaml"
            ((System.Windows.Controls.Border)(target)).MouseLeftButtonDown += new System.Windows.Input.MouseButtonEventHandler(this.HeaderBar_MouseLeftButtonDown);
            
            #line default
            #line hidden
            return;
            case 2:
            this.CloseButton = ((System.Windows.Controls.Button)(target));
            
            #line 258 "..\..\..\MainWindow.xaml"
            this.CloseButton.Click += new System.Windows.RoutedEventHandler(this.CloseButton_Click);
            
            #line default
            #line hidden
            return;
            case 3:
            this.MaximizeButton = ((System.Windows.Controls.Button)(target));
            
            #line 259 "..\..\..\MainWindow.xaml"
            this.MaximizeButton.Click += new System.Windows.RoutedEventHandler(this.MaximizeButton_Click);
            
            #line default
            #line hidden
            return;
            case 4:
            this.MinimizeButton = ((System.Windows.Controls.Button)(target));
            
            #line 260 "..\..\..\MainWindow.xaml"
            this.MinimizeButton.Click += new System.Windows.RoutedEventHandler(this.MinimizeButton_Click);
            
            #line default
            #line hidden
            return;
            case 5:
            this.CloudStorageButton = ((System.Windows.Controls.Button)(target));
            
            #line 305 "..\..\..\MainWindow.xaml"
            this.CloudStorageButton.Click += new System.Windows.RoutedEventHandler(this.CloudStorageButton_Click);
            
            #line default
            #line hidden
            return;
            case 6:
            this.DashboardButton = ((System.Windows.Controls.Button)(target));
            
            #line 342 "..\..\..\MainWindow.xaml"
            this.DashboardButton.Click += new System.Windows.RoutedEventHandler(this.NavigationButton_Click);
            
            #line default
            #line hidden
            return;
            case 7:
            this.InvoicesButton = ((System.Windows.Controls.Button)(target));
            
            #line 348 "..\..\..\MainWindow.xaml"
            this.InvoicesButton.Click += new System.Windows.RoutedEventHandler(this.NavigationButton_Click);
            
            #line default
            #line hidden
            return;
            case 8:
            this.PaymentsButton = ((System.Windows.Controls.Button)(target));
            
            #line 354 "..\..\..\MainWindow.xaml"
            this.PaymentsButton.Click += new System.Windows.RoutedEventHandler(this.NavigationButton_Click);
            
            #line default
            #line hidden
            return;
            case 9:
            this.SuppliersButton = ((System.Windows.Controls.Button)(target));
            
            #line 360 "..\..\..\MainWindow.xaml"
            this.SuppliersButton.Click += new System.Windows.RoutedEventHandler(this.NavigationButton_Click);
            
            #line default
            #line hidden
            return;
            case 10:
            this.SearchButton = ((System.Windows.Controls.Button)(target));
            
            #line 366 "..\..\..\MainWindow.xaml"
            this.SearchButton.Click += new System.Windows.RoutedEventHandler(this.NavigationButton_Click);
            
            #line default
            #line hidden
            return;
            case 11:
            this.ReportsButton = ((System.Windows.Controls.Button)(target));
            
            #line 372 "..\..\..\MainWindow.xaml"
            this.ReportsButton.Click += new System.Windows.RoutedEventHandler(this.NavigationButton_Click);
            
            #line default
            #line hidden
            return;
            case 12:
            this.SettingsButton = ((System.Windows.Controls.Button)(target));
            
            #line 395 "..\..\..\MainWindow.xaml"
            this.SettingsButton.Click += new System.Windows.RoutedEventHandler(this.NavigationButton_Click);
            
            #line default
            #line hidden
            return;
            case 13:
            this.ImportExportButton = ((System.Windows.Controls.Button)(target));
            
            #line 401 "..\..\..\MainWindow.xaml"
            this.ImportExportButton.Click += new System.Windows.RoutedEventHandler(this.NavigationButton_Click);
            
            #line default
            #line hidden
            return;
            case 14:
            this.BackupRestoreButton = ((System.Windows.Controls.Button)(target));
            
            #line 407 "..\..\..\MainWindow.xaml"
            this.BackupRestoreButton.Click += new System.Windows.RoutedEventHandler(this.NavigationButton_Click);
            
            #line default
            #line hidden
            return;
            case 15:
            this.PerformanceButton = ((System.Windows.Controls.Button)(target));
            
            #line 413 "..\..\..\MainWindow.xaml"
            this.PerformanceButton.Click += new System.Windows.RoutedEventHandler(this.NavigationButton_Click);
            
            #line default
            #line hidden
            return;
            case 16:
            this.SidebarCloudButton = ((System.Windows.Controls.Button)(target));
            
            #line 440 "..\..\..\MainWindow.xaml"
            this.SidebarCloudButton.Click += new System.Windows.RoutedEventHandler(this.CloudStorageButton_Click);
            
            #line default
            #line hidden
            return;
            case 17:
            this.MainContentPresenter = ((System.Windows.Controls.ContentPresenter)(target));
            return;
            }
            this._contentLoaded = true;
        }
    }
}

