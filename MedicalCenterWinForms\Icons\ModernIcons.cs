using System.Drawing;
using System.Drawing.Drawing2D;

namespace MedicalCenterWinForms.Icons
{
    /// <summary>
    /// Modern Icon System for Medical Center Application
    /// Provides scalable vector icons with medical theme
    /// </summary>
    public static class ModernIcons
    {
        // Icon Categories
        public static class Medical
        {
            public const string Hospital = "🏥";
            public const string Doctor = "👨‍⚕️";
            public const string Patient = "🧑‍🦽";
            public const string Stethoscope = "🩺";
            public const string Pill = "💊";
            public const string Syringe = "💉";
            public const string Ambulance = "🚑";
            public const string FirstAid = "🩹";
            public const string Microscope = "🔬";
            public const string XRay = "🦴";
            public const string Heart = "❤️";
            public const string Thermometer = "🌡️";
        }

        public static class Navigation
        {
            public const string Home = "🏠";
            public const string Dashboard = "📊";
            public const string Settings = "⚙️";
            public const string Search = "🔍";
            public const string Add = "➕";
            public const string Edit = "✏️";
            public const string Delete = "🗑️";
            public const string Save = "💾";
            public const string Print = "🖨️";
            public const string Export = "📤";
            public const string Import = "📥";
            public const string Refresh = "🔄";
        }

        public static class User
        {
            public const string Profile = "👤";
            public const string Login = "🔐";
            public const string Logout = "🚪";
            public const string Admin = "👑";
            public const string Staff = "👥";
            public const string Security = "🔒";
            public const string Key = "🔑";
        }

        public static class Status
        {
            public const string Success = "✅";
            public const string Warning = "⚠️";
            public const string Error = "❌";
            public const string Info = "ℹ️";
            public const string Question = "❓";
            public const string Clock = "🕐";
            public const string Calendar = "📅";
            public const string Bell = "🔔";
        }

        public static class Actions
        {
            public const string Play = "▶️";
            public const string Pause = "⏸️";
            public const string Stop = "⏹️";
            public const string Forward = "⏭️";
            public const string Backward = "⏮️";
            public const string Up = "⬆️";
            public const string Down = "⬇️";
            public const string Left = "⬅️";
            public const string Right = "➡️";
        }

        // Create SVG-like vector icons
        public static class Vector
        {
            // Create a modern medical cross icon
            public static Bitmap CreateMedicalCross(int size, Color color)
            {
                var bitmap = new Bitmap(size, size);
                using var graphics = Graphics.FromImage(bitmap);
                graphics.SmoothingMode = SmoothingMode.AntiAlias;

                var crossSize = size * 0.6f;
                var crossThickness = crossSize * 0.2f;
                var centerX = size / 2f;
                var centerY = size / 2f;

                // Vertical bar
                var verticalRect = new RectangleF(
                    centerX - crossThickness / 2,
                    centerY - crossSize / 2,
                    crossThickness,
                    crossSize
                );

                // Horizontal bar
                var horizontalRect = new RectangleF(
                    centerX - crossSize / 2,
                    centerY - crossThickness / 2,
                    crossSize,
                    crossThickness
                );

                using var brush = new SolidBrush(color);
                graphics.FillRectangle(brush, verticalRect);
                graphics.FillRectangle(brush, horizontalRect);

                return bitmap;
            }

            // Create a modern user icon
            public static Bitmap CreateUserIcon(int size, Color color)
            {
                var bitmap = new Bitmap(size, size);
                using var graphics = Graphics.FromImage(bitmap);
                graphics.SmoothingMode = SmoothingMode.AntiAlias;

                var centerX = size / 2f;
                var centerY = size / 2f;

                // Head (circle)
                var headRadius = size * 0.15f;
                var headRect = new RectangleF(
                    centerX - headRadius,
                    centerY - size * 0.25f,
                    headRadius * 2,
                    headRadius * 2
                );

                // Body (rounded rectangle)
                var bodyWidth = size * 0.4f;
                var bodyHeight = size * 0.35f;
                var bodyRect = new RectangleF(
                    centerX - bodyWidth / 2,
                    centerY - size * 0.05f,
                    bodyWidth,
                    bodyHeight
                );

                using var brush = new SolidBrush(color);
                graphics.FillEllipse(brush, headRect);
                graphics.FillRectangle(brush, bodyRect);

                return bitmap;
            }

            // Create a modern settings gear icon
            public static Bitmap CreateSettingsIcon(int size, Color color)
            {
                var bitmap = new Bitmap(size, size);
                using var graphics = Graphics.FromImage(bitmap);
                graphics.SmoothingMode = SmoothingMode.AntiAlias;

                var centerX = size / 2f;
                var centerY = size / 2f;
                var outerRadius = size * 0.4f;
                var innerRadius = size * 0.15f;
                var teethCount = 8;

                using var path = new GraphicsPath();

                // Create gear teeth
                for (int i = 0; i < teethCount; i++)
                {
                    var angle1 = (float)(i * 2 * Math.PI / teethCount);
                    var angle2 = (float)((i + 0.3) * 2 * Math.PI / teethCount);
                    var angle3 = (float)((i + 0.7) * 2 * Math.PI / teethCount);
                    var angle4 = (float)((i + 1) * 2 * Math.PI / teethCount);

                    var x1 = centerX + (float)(Math.Cos(angle1) * outerRadius);
                    var y1 = centerY + (float)(Math.Sin(angle1) * outerRadius);
                    var x2 = centerX + (float)(Math.Cos(angle2) * outerRadius);
                    var y2 = centerY + (float)(Math.Sin(angle2) * outerRadius);
                    var x3 = centerX + (float)(Math.Cos(angle3) * innerRadius * 1.2);
                    var y3 = centerY + (float)(Math.Sin(angle3) * innerRadius * 1.2);
                    var x4 = centerX + (float)(Math.Cos(angle4) * innerRadius * 1.2);
                    var y4 = centerY + (float)(Math.Sin(angle4) * innerRadius * 1.2);

                    if (i == 0)
                        path.StartFigure();

                    path.AddLine(x1, y1, x2, y2);
                    path.AddLine(x2, y2, x3, y3);
                    path.AddLine(x3, y3, x4, y4);
                }

                path.CloseFigure();

                // Draw gear
                using var brush = new SolidBrush(color);
                graphics.FillPath(brush, path);

                // Draw center hole
                var holeRect = new RectangleF(
                    centerX - innerRadius,
                    centerY - innerRadius,
                    innerRadius * 2,
                    innerRadius * 2
                );

                using var holeBrush = new SolidBrush(Color.Transparent);
                graphics.CompositingMode = CompositingMode.SourceCopy;
                graphics.FillEllipse(holeBrush, holeRect);

                return bitmap;
            }

            // Create a modern dashboard icon
            public static Bitmap CreateDashboardIcon(int size, Color color)
            {
                var bitmap = new Bitmap(size, size);
                using var graphics = Graphics.FromImage(bitmap);
                graphics.SmoothingMode = SmoothingMode.AntiAlias;

                var margin = size * 0.1f;
                var rectSize = (size - margin * 3) / 2;

                // Four rectangles representing dashboard panels
                var rects = new RectangleF[]
                {
                    new RectangleF(margin, margin, rectSize, rectSize),
                    new RectangleF(margin * 2 + rectSize, margin, rectSize, rectSize),
                    new RectangleF(margin, margin * 2 + rectSize, rectSize, rectSize),
                    new RectangleF(margin * 2 + rectSize, margin * 2 + rectSize, rectSize, rectSize)
                };

                using var brush = new SolidBrush(color);
                foreach (var rect in rects)
                {
                    graphics.FillRectangle(brush, rect);
                }

                return bitmap;
            }

            // Create a modern search icon
            public static Bitmap CreateSearchIcon(int size, Color color)
            {
                var bitmap = new Bitmap(size, size);
                using var graphics = Graphics.FromImage(bitmap);
                graphics.SmoothingMode = SmoothingMode.AntiAlias;

                var centerX = size * 0.4f;
                var centerY = size * 0.4f;
                var radius = size * 0.25f;
                var strokeWidth = size * 0.05f;

                // Draw magnifying glass circle
                using var pen = new Pen(color, strokeWidth);
                graphics.DrawEllipse(pen, centerX - radius, centerY - radius, radius * 2, radius * 2);

                // Draw handle
                var handleStartX = centerX + radius * 0.7f;
                var handleStartY = centerY + radius * 0.7f;
                var handleEndX = size * 0.85f;
                var handleEndY = size * 0.85f;

                graphics.DrawLine(pen, handleStartX, handleStartY, handleEndX, handleEndY);

                return bitmap;
            }
        }

        // Helper methods for icon management
        public static class Utils
        {
            // Get icon by name with fallback
            public static string GetIcon(string category, string name, string fallback = "❓")
            {
                try
                {
                    var type = typeof(ModernIcons).GetNestedType(category);
                    if (type != null)
                    {
                        var field = type.GetField(name);
                        if (field != null)
                        {
                            return field.GetValue(null)?.ToString() ?? fallback;
                        }
                    }
                }
                catch
                {
                    // Return fallback on any error
                }
                return fallback;
            }

            // Create icon label with modern styling
            public static Label CreateIconLabel(string icon, int size = 24, Color? color = null)
            {
                return new Label
                {
                    Text = icon,
                    Font = new Font("Segoe UI Emoji", size, FontStyle.Regular),
                    ForeColor = color ?? Color.Black,
                    AutoSize = true,
                    TextAlign = ContentAlignment.MiddleCenter
                };
            }

            // Create icon button with modern styling
            public static Button CreateIconButton(string icon, string text = "", int iconSize = 16)
            {
                var button = new Button
                {
                    Text = string.IsNullOrEmpty(text) ? icon : $"{icon} {text}",
                    Font = new Font("Segoe UI", iconSize, FontStyle.Regular),
                    FlatStyle = FlatStyle.Flat,
                    FlatAppearance = { BorderSize = 0 },
                    UseVisualStyleBackColor = true,
                    Cursor = Cursors.Hand
                };

                return button;
            }

            // Get medical icon by context
            public static string GetMedicalIcon(string context)
            {
                return context.ToLower() switch
                {
                    "doctor" or "physician" => Medical.Doctor,
                    "patient" => Medical.Patient,
                    "hospital" => Medical.Hospital,
                    "medicine" or "medication" => Medical.Pill,
                    "injection" => Medical.Syringe,
                    "emergency" => Medical.Ambulance,
                    "diagnosis" => Medical.Stethoscope,
                    "lab" or "laboratory" => Medical.Microscope,
                    "xray" or "radiology" => Medical.XRay,
                    "cardiology" or "heart" => Medical.Heart,
                    "temperature" or "fever" => Medical.Thermometer,
                    _ => Medical.Hospital
                };
            }
        }

        /// <summary>
        /// Get application icon for the medical center
        /// </summary>
        /// <returns>Application icon</returns>
        public static Icon GetApplicationIcon()
        {
            try
            {
                // Create a simple medical cross icon
                using (var bitmap = new Bitmap(32, 32))
                using (var graphics = Graphics.FromImage(bitmap))
                {
                    graphics.SmoothingMode = SmoothingMode.AntiAlias;
                    graphics.Clear(Color.Transparent);

                    // Draw medical cross
                    using (var brush = new SolidBrush(Color.FromArgb(37, 99, 235))) // Medical blue
                    {
                        // Vertical bar
                        graphics.FillRectangle(brush, 12, 4, 8, 24);
                        // Horizontal bar
                        graphics.FillRectangle(brush, 4, 12, 24, 8);
                    }

                    return Icon.FromHandle(bitmap.GetHicon());
                }
            }
            catch
            {
                // Return default icon if creation fails
                return SystemIcons.Application;
            }
        }
    }
}
