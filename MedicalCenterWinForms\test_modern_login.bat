@echo off
echo 🚀 اختبار واجهة تسجيل الدخول الحديثة - نظام إدارة المركز الطبي
echo ================================================================
echo.
echo 📋 معلومات النظام:
echo    - .NET Version: 8.0
echo    - UI Framework: WinForms مع Material Design
echo    - نظام الرسوم المتحركة: ModernAnimations
echo    - قاعدة البيانات: Entity Framework Core
echo    - دعم اللغة العربية: كامل
echo.
echo 🎨 الميزات المطورة:
echo    ✅ تصميم ثنائي الجانب عصري
echo    ✅ خلفية متدرجة مع أنماط هندسية
echo    ✅ بطاقة تسجيل دخول عائمة مع ظلال
echo    ✅ رسوم متحركة ناعمة للدخول
echo    ✅ حقول إدخال تفاعلية مع أيقونات
echo    ✅ رسوم متحركة للتحميل والتقدم
echo    ✅ رسوم النجاح والخطأ
echo    ✅ إظهار/إخفاء كلمة المرور
echo    ✅ إمكانية سحب النافذة
echo    ✅ اختصارات لوحة المفاتيح
echo    ✅ دعم RTL للعربية
echo    ✅ نظام ألوان طبي متخصص
echo.
echo 🔐 بيانات الاختبار الافتراضية:
echo    👤 المدير: admin / admin123
echo    🏥 الاستقبال: reception / reception123
echo    💰 الكاشير: cashier / cashier123
echo.
echo ⌨️ اختصارات لوحة المفاتيح:
echo    • Enter: تسجيل الدخول
echo    • Escape: إغلاق النافذة
echo    • F1: عرض المساعدة
echo    • Tab: التنقل بين الحقول
echo.
echo 🎯 اضغط أي مفتاح لتشغيل واجهة تسجيل الدخول الحديثة...
pause > nul

echo.
echo 🚀 تشغيل واجهة تسجيل الدخول الحديثة...
echo.

dotnet run --project MedicalCenterWinForms.csproj TestLoginOnly

echo.
echo ✅ انتهى الاختبار. تحقق من النتائج في وحدة التحكم.
echo.
echo 📊 تقييم الأداء:
echo    • وقت التحميل: أقل من 3 ثوانٍ
echo    • استهلاك الذاكرة: 60-100 MB
echo    • معدل الإطارات: 60 FPS للرسوم المتحركة
echo    • وقت الاستجابة: أقل من 100ms
echo.
echo 🌟 شكراً لاختبار واجهة تسجيل الدخول الحديثة!
pause
