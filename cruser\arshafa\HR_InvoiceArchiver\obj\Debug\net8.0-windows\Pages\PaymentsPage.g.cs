﻿#pragma checksum "..\..\..\..\Pages\PaymentsPage.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "C3B6D19A75D4B3F47DC96EDC7682F53B985DD767"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using HR_InvoiceArchiver.Converters;
using MaterialDesignThemes.Wpf;
using MaterialDesignThemes.Wpf.Converters;
using MaterialDesignThemes.Wpf.Transitions;
using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace HR_InvoiceArchiver.Pages {
    
    
    /// <summary>
    /// PaymentsPage
    /// </summary>
    public partial class PaymentsPage : System.Windows.Controls.UserControl, System.Windows.Markup.IComponentConnector, System.Windows.Markup.IStyleConnector {
        
        
        #line 134 "..\..\..\..\Pages\PaymentsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock HeaderTotalPaymentsText;
        
        #line default
        #line hidden
        
        
        #line 145 "..\..\..\..\Pages\PaymentsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock HeaderTotalAmountText;
        
        #line default
        #line hidden
        
        
        #line 191 "..\..\..\..\Pages\PaymentsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button AddPaymentButton;
        
        #line default
        #line hidden
        
        
        #line 203 "..\..\..\..\Pages\PaymentsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button AddMultiPaymentButton;
        
        #line default
        #line hidden
        
        
        #line 218 "..\..\..\..\Pages\PaymentsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button RefreshButton;
        
        #line default
        #line hidden
        
        
        #line 226 "..\..\..\..\Pages\PaymentsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ExportButton;
        
        #line default
        #line hidden
        
        
        #line 234 "..\..\..\..\Pages\PaymentsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button CompactViewToggle;
        
        #line default
        #line hidden
        
        
        #line 256 "..\..\..\..\Pages\PaymentsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox SearchTextBox;
        
        #line default
        #line hidden
        
        
        #line 265 "..\..\..\..\Pages\PaymentsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button TodayFilterButton;
        
        #line default
        #line hidden
        
        
        #line 271 "..\..\..\..\Pages\PaymentsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button WeekFilterButton;
        
        #line default
        #line hidden
        
        
        #line 277 "..\..\..\..\Pages\PaymentsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button MonthFilterButton;
        
        #line default
        #line hidden
        
        
        #line 294 "..\..\..\..\Pages\PaymentsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ToggleFiltersButton;
        
        #line default
        #line hidden
        
        
        #line 302 "..\..\..\..\Pages\PaymentsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button KeyboardShortcutsButton;
        
        #line default
        #line hidden
        
        
        #line 314 "..\..\..\..\Pages\PaymentsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Border FiltersPanel;
        
        #line default
        #line hidden
        
        
        #line 344 "..\..\..\..\Pages\PaymentsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DatePicker FromDatePicker;
        
        #line default
        #line hidden
        
        
        #line 352 "..\..\..\..\Pages\PaymentsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DatePicker ToDatePicker;
        
        #line default
        #line hidden
        
        
        #line 361 "..\..\..\..\Pages\PaymentsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox MinAmountTextBox;
        
        #line default
        #line hidden
        
        
        #line 369 "..\..\..\..\Pages\PaymentsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox MaxAmountTextBox;
        
        #line default
        #line hidden
        
        
        #line 378 "..\..\..\..\Pages\PaymentsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox PaymentMethodComboBox;
        
        #line default
        #line hidden
        
        
        #line 391 "..\..\..\..\Pages\PaymentsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox PaymentStatusComboBox;
        
        #line default
        #line hidden
        
        
        #line 405 "..\..\..\..\Pages\PaymentsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ApplyFiltersButton;
        
        #line default
        #line hidden
        
        
        #line 419 "..\..\..\..\Pages\PaymentsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ClearFiltersButton;
        
        #line default
        #line hidden
        
        
        #line 459 "..\..\..\..\Pages\PaymentsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock PaymentCountText;
        
        #line default
        #line hidden
        
        
        #line 465 "..\..\..\..\Pages\PaymentsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Primitives.ToggleButton CompactModeToggle;
        
        #line default
        #line hidden
        
        
        #line 475 "..\..\..\..\Pages\PaymentsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DataGrid PaymentsDataGrid;
        
        #line default
        #line hidden
        
        
        #line 829 "..\..\..\..\Pages\PaymentsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock FooterPaymentCountText;
        
        #line default
        #line hidden
        
        
        #line 842 "..\..\..\..\Pages\PaymentsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TotalDisplayedAmountText;
        
        #line default
        #line hidden
        
        
        #line 855 "..\..\..\..\Pages\PaymentsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock LastUpdateText;
        
        #line default
        #line hidden
        
        
        #line 865 "..\..\..\..\Pages\PaymentsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid LoadingPanel;
        
        #line default
        #line hidden
        
        
        #line 901 "..\..\..\..\Pages\PaymentsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid EmptyStatePanel;
        
        #line default
        #line hidden
        
        
        #line 943 "..\..\..\..\Pages\PaymentsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid MultiPaymentOverlay;
        
        #line default
        #line hidden
        
        
        #line 960 "..\..\..\..\Pages\PaymentsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid MultiPaymentContainer;
        
        #line default
        #line hidden
        
        
        #line 967 "..\..\..\..\Pages\PaymentsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid PaymentDetailsOverlay;
        
        #line default
        #line hidden
        
        
        #line 984 "..\..\..\..\Pages\PaymentsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid PaymentDetailsContainer;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "*******")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/HR_InvoiceArchiver;component/pages/paymentspage.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\Pages\PaymentsPage.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "*******")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            
            #line 9 "..\..\..\..\Pages\PaymentsPage.xaml"
            ((HR_InvoiceArchiver.Pages.PaymentsPage)(target)).KeyDown += new System.Windows.Input.KeyEventHandler(this.PaymentsPage_KeyDown);
            
            #line default
            #line hidden
            return;
            case 2:
            this.HeaderTotalPaymentsText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 3:
            this.HeaderTotalAmountText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 4:
            this.AddPaymentButton = ((System.Windows.Controls.Button)(target));
            
            #line 196 "..\..\..\..\Pages\PaymentsPage.xaml"
            this.AddPaymentButton.Click += new System.Windows.RoutedEventHandler(this.AddPaymentButton_Click);
            
            #line default
            #line hidden
            return;
            case 5:
            this.AddMultiPaymentButton = ((System.Windows.Controls.Button)(target));
            
            #line 208 "..\..\..\..\Pages\PaymentsPage.xaml"
            this.AddMultiPaymentButton.Click += new System.Windows.RoutedEventHandler(this.AddMultiPaymentButton_Click);
            
            #line default
            #line hidden
            return;
            case 6:
            this.RefreshButton = ((System.Windows.Controls.Button)(target));
            
            #line 222 "..\..\..\..\Pages\PaymentsPage.xaml"
            this.RefreshButton.Click += new System.Windows.RoutedEventHandler(this.RefreshButton_Click);
            
            #line default
            #line hidden
            return;
            case 7:
            this.ExportButton = ((System.Windows.Controls.Button)(target));
            
            #line 230 "..\..\..\..\Pages\PaymentsPage.xaml"
            this.ExportButton.Click += new System.Windows.RoutedEventHandler(this.ExportButton_Click);
            
            #line default
            #line hidden
            return;
            case 8:
            this.CompactViewToggle = ((System.Windows.Controls.Button)(target));
            
            #line 238 "..\..\..\..\Pages\PaymentsPage.xaml"
            this.CompactViewToggle.Click += new System.Windows.RoutedEventHandler(this.CompactViewToggle_Click);
            
            #line default
            #line hidden
            return;
            case 9:
            this.SearchTextBox = ((System.Windows.Controls.TextBox)(target));
            
            #line 261 "..\..\..\..\Pages\PaymentsPage.xaml"
            this.SearchTextBox.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.SearchTextBox_TextChanged);
            
            #line default
            #line hidden
            return;
            case 10:
            this.TodayFilterButton = ((System.Windows.Controls.Button)(target));
            
            #line 270 "..\..\..\..\Pages\PaymentsPage.xaml"
            this.TodayFilterButton.Click += new System.Windows.RoutedEventHandler(this.TodayFilter_Click);
            
            #line default
            #line hidden
            return;
            case 11:
            this.WeekFilterButton = ((System.Windows.Controls.Button)(target));
            
            #line 276 "..\..\..\..\Pages\PaymentsPage.xaml"
            this.WeekFilterButton.Click += new System.Windows.RoutedEventHandler(this.WeekFilter_Click);
            
            #line default
            #line hidden
            return;
            case 12:
            this.MonthFilterButton = ((System.Windows.Controls.Button)(target));
            
            #line 282 "..\..\..\..\Pages\PaymentsPage.xaml"
            this.MonthFilterButton.Click += new System.Windows.RoutedEventHandler(this.MonthFilter_Click);
            
            #line default
            #line hidden
            return;
            case 13:
            
            #line 290 "..\..\..\..\Pages\PaymentsPage.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ClearSearchButton_Click);
            
            #line default
            #line hidden
            return;
            case 14:
            this.ToggleFiltersButton = ((System.Windows.Controls.Button)(target));
            
            #line 298 "..\..\..\..\Pages\PaymentsPage.xaml"
            this.ToggleFiltersButton.Click += new System.Windows.RoutedEventHandler(this.ToggleFilters_Click);
            
            #line default
            #line hidden
            return;
            case 15:
            this.KeyboardShortcutsButton = ((System.Windows.Controls.Button)(target));
            
            #line 306 "..\..\..\..\Pages\PaymentsPage.xaml"
            this.KeyboardShortcutsButton.Click += new System.Windows.RoutedEventHandler(this.KeyboardShortcuts_Click);
            
            #line default
            #line hidden
            return;
            case 16:
            this.FiltersPanel = ((System.Windows.Controls.Border)(target));
            return;
            case 17:
            this.FromDatePicker = ((System.Windows.Controls.DatePicker)(target));
            
            #line 347 "..\..\..\..\Pages\PaymentsPage.xaml"
            this.FromDatePicker.SelectedDateChanged += new System.EventHandler<System.Windows.Controls.SelectionChangedEventArgs>(this.DateFilter_Changed);
            
            #line default
            #line hidden
            return;
            case 18:
            this.ToDatePicker = ((System.Windows.Controls.DatePicker)(target));
            
            #line 355 "..\..\..\..\Pages\PaymentsPage.xaml"
            this.ToDatePicker.SelectedDateChanged += new System.EventHandler<System.Windows.Controls.SelectionChangedEventArgs>(this.DateFilter_Changed);
            
            #line default
            #line hidden
            return;
            case 19:
            this.MinAmountTextBox = ((System.Windows.Controls.TextBox)(target));
            
            #line 364 "..\..\..\..\Pages\PaymentsPage.xaml"
            this.MinAmountTextBox.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.AmountFilter_Changed);
            
            #line default
            #line hidden
            return;
            case 20:
            this.MaxAmountTextBox = ((System.Windows.Controls.TextBox)(target));
            
            #line 372 "..\..\..\..\Pages\PaymentsPage.xaml"
            this.MaxAmountTextBox.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.AmountFilter_Changed);
            
            #line default
            #line hidden
            return;
            case 21:
            this.PaymentMethodComboBox = ((System.Windows.Controls.ComboBox)(target));
            
            #line 381 "..\..\..\..\Pages\PaymentsPage.xaml"
            this.PaymentMethodComboBox.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.PaymentMethodFilter_Changed);
            
            #line default
            #line hidden
            return;
            case 22:
            this.PaymentStatusComboBox = ((System.Windows.Controls.ComboBox)(target));
            
            #line 394 "..\..\..\..\Pages\PaymentsPage.xaml"
            this.PaymentStatusComboBox.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.PaymentStatusFilter_Changed);
            
            #line default
            #line hidden
            return;
            case 23:
            this.ApplyFiltersButton = ((System.Windows.Controls.Button)(target));
            
            #line 412 "..\..\..\..\Pages\PaymentsPage.xaml"
            this.ApplyFiltersButton.Click += new System.Windows.RoutedEventHandler(this.ApplyFilters_Click);
            
            #line default
            #line hidden
            return;
            case 24:
            this.ClearFiltersButton = ((System.Windows.Controls.Button)(target));
            
            #line 425 "..\..\..\..\Pages\PaymentsPage.xaml"
            this.ClearFiltersButton.Click += new System.Windows.RoutedEventHandler(this.ClearFilters_Click);
            
            #line default
            #line hidden
            return;
            case 25:
            this.PaymentCountText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 26:
            this.CompactModeToggle = ((System.Windows.Controls.Primitives.ToggleButton)(target));
            
            #line 469 "..\..\..\..\Pages\PaymentsPage.xaml"
            this.CompactModeToggle.Checked += new System.Windows.RoutedEventHandler(this.CompactMode_Changed);
            
            #line default
            #line hidden
            
            #line 470 "..\..\..\..\Pages\PaymentsPage.xaml"
            this.CompactModeToggle.Unchecked += new System.Windows.RoutedEventHandler(this.CompactMode_Changed);
            
            #line default
            #line hidden
            return;
            case 27:
            this.PaymentsDataGrid = ((System.Windows.Controls.DataGrid)(target));
            return;
            case 32:
            this.FooterPaymentCountText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 33:
            this.TotalDisplayedAmountText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 34:
            this.LastUpdateText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 35:
            this.LoadingPanel = ((System.Windows.Controls.Grid)(target));
            return;
            case 36:
            this.EmptyStatePanel = ((System.Windows.Controls.Grid)(target));
            return;
            case 37:
            
            #line 921 "..\..\..\..\Pages\PaymentsPage.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.AddPaymentButton_Click);
            
            #line default
            #line hidden
            return;
            case 38:
            
            #line 931 "..\..\..\..\Pages\PaymentsPage.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ClearSearchButton_Click);
            
            #line default
            #line hidden
            return;
            case 39:
            this.MultiPaymentOverlay = ((System.Windows.Controls.Grid)(target));
            return;
            case 40:
            this.MultiPaymentContainer = ((System.Windows.Controls.Grid)(target));
            return;
            case 41:
            this.PaymentDetailsOverlay = ((System.Windows.Controls.Grid)(target));
            return;
            case 42:
            this.PaymentDetailsContainer = ((System.Windows.Controls.Grid)(target));
            return;
            }
            this._contentLoaded = true;
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "*******")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        void System.Windows.Markup.IStyleConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 28:
            
            #line 715 "..\..\..\..\Pages\PaymentsPage.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ViewReceiptAttachment_Click);
            
            #line default
            #line hidden
            break;
            case 29:
            
            #line 740 "..\..\..\..\Pages\PaymentsPage.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ViewPaymentDetails_Click);
            
            #line default
            #line hidden
            break;
            case 30:
            
            #line 764 "..\..\..\..\Pages\PaymentsPage.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.EditPayment_Click);
            
            #line default
            #line hidden
            break;
            case 31:
            
            #line 786 "..\..\..\..\Pages\PaymentsPage.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.DeletePayment_Click);
            
            #line default
            #line hidden
            break;
            }
        }
    }
}

