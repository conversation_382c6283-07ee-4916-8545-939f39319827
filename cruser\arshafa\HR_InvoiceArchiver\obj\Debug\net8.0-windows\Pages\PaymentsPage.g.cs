﻿#pragma checksum "..\..\..\..\Pages\PaymentsPage.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "243521D7EE384130F1064D4711C989CA7D410349"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using HR_InvoiceArchiver.Converters;
using MaterialDesignThemes.Wpf;
using MaterialDesignThemes.Wpf.Converters;
using MaterialDesignThemes.Wpf.Transitions;
using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace HR_InvoiceArchiver.Pages {
    
    
    /// <summary>
    /// PaymentsPage
    /// </summary>
    public partial class PaymentsPage : System.Windows.Controls.UserControl, System.Windows.Markup.IComponentConnector, System.Windows.Markup.IStyleConnector {
        
        
        #line 138 "..\..\..\..\Pages\PaymentsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock HeaderTotalPaymentsText;
        
        #line default
        #line hidden
        
        
        #line 146 "..\..\..\..\Pages\PaymentsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock HeaderTotalAmountText;
        
        #line default
        #line hidden
        
        
        #line 197 "..\..\..\..\Pages\PaymentsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TotalPaymentsText;
        
        #line default
        #line hidden
        
        
        #line 219 "..\..\..\..\Pages\PaymentsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TotalAmountText;
        
        #line default
        #line hidden
        
        
        #line 242 "..\..\..\..\Pages\PaymentsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock CashPaymentsText;
        
        #line default
        #line hidden
        
        
        #line 263 "..\..\..\..\Pages\PaymentsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock CardPaymentsText;
        
        #line default
        #line hidden
        
        
        #line 291 "..\..\..\..\Pages\PaymentsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button AddPaymentButton;
        
        #line default
        #line hidden
        
        
        #line 303 "..\..\..\..\Pages\PaymentsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button AddMultiPaymentButton;
        
        #line default
        #line hidden
        
        
        #line 318 "..\..\..\..\Pages\PaymentsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button RefreshButton;
        
        #line default
        #line hidden
        
        
        #line 326 "..\..\..\..\Pages\PaymentsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ExportButton;
        
        #line default
        #line hidden
        
        
        #line 334 "..\..\..\..\Pages\PaymentsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button CompactViewToggle;
        
        #line default
        #line hidden
        
        
        #line 356 "..\..\..\..\Pages\PaymentsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox SearchTextBox;
        
        #line default
        #line hidden
        
        
        #line 365 "..\..\..\..\Pages\PaymentsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button TodayFilterButton;
        
        #line default
        #line hidden
        
        
        #line 371 "..\..\..\..\Pages\PaymentsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button WeekFilterButton;
        
        #line default
        #line hidden
        
        
        #line 377 "..\..\..\..\Pages\PaymentsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button MonthFilterButton;
        
        #line default
        #line hidden
        
        
        #line 394 "..\..\..\..\Pages\PaymentsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ToggleFiltersButton;
        
        #line default
        #line hidden
        
        
        #line 402 "..\..\..\..\Pages\PaymentsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button KeyboardShortcutsButton;
        
        #line default
        #line hidden
        
        
        #line 414 "..\..\..\..\Pages\PaymentsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Border FiltersPanel;
        
        #line default
        #line hidden
        
        
        #line 444 "..\..\..\..\Pages\PaymentsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DatePicker FromDatePicker;
        
        #line default
        #line hidden
        
        
        #line 452 "..\..\..\..\Pages\PaymentsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DatePicker ToDatePicker;
        
        #line default
        #line hidden
        
        
        #line 461 "..\..\..\..\Pages\PaymentsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox MinAmountTextBox;
        
        #line default
        #line hidden
        
        
        #line 469 "..\..\..\..\Pages\PaymentsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox MaxAmountTextBox;
        
        #line default
        #line hidden
        
        
        #line 478 "..\..\..\..\Pages\PaymentsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox PaymentMethodComboBox;
        
        #line default
        #line hidden
        
        
        #line 491 "..\..\..\..\Pages\PaymentsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox PaymentStatusComboBox;
        
        #line default
        #line hidden
        
        
        #line 505 "..\..\..\..\Pages\PaymentsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ApplyFiltersButton;
        
        #line default
        #line hidden
        
        
        #line 519 "..\..\..\..\Pages\PaymentsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ClearFiltersButton;
        
        #line default
        #line hidden
        
        
        #line 554 "..\..\..\..\Pages\PaymentsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock PaymentCountText;
        
        #line default
        #line hidden
        
        
        #line 558 "..\..\..\..\Pages\PaymentsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Primitives.ToggleButton CompactModeToggle;
        
        #line default
        #line hidden
        
        
        #line 569 "..\..\..\..\Pages\PaymentsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DataGrid PaymentsDataGrid;
        
        #line default
        #line hidden
        
        
        #line 923 "..\..\..\..\Pages\PaymentsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock FooterPaymentCountText;
        
        #line default
        #line hidden
        
        
        #line 936 "..\..\..\..\Pages\PaymentsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TotalDisplayedAmountText;
        
        #line default
        #line hidden
        
        
        #line 949 "..\..\..\..\Pages\PaymentsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock LastUpdateText;
        
        #line default
        #line hidden
        
        
        #line 959 "..\..\..\..\Pages\PaymentsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid LoadingPanel;
        
        #line default
        #line hidden
        
        
        #line 995 "..\..\..\..\Pages\PaymentsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid EmptyStatePanel;
        
        #line default
        #line hidden
        
        
        #line 1037 "..\..\..\..\Pages\PaymentsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid MultiPaymentOverlay;
        
        #line default
        #line hidden
        
        
        #line 1054 "..\..\..\..\Pages\PaymentsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid MultiPaymentContainer;
        
        #line default
        #line hidden
        
        
        #line 1061 "..\..\..\..\Pages\PaymentsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid PaymentDetailsOverlay;
        
        #line default
        #line hidden
        
        
        #line 1078 "..\..\..\..\Pages\PaymentsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid PaymentDetailsContainer;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "*******")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/HR_InvoiceArchiver;component/pages/paymentspage.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\Pages\PaymentsPage.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "*******")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            
            #line 9 "..\..\..\..\Pages\PaymentsPage.xaml"
            ((HR_InvoiceArchiver.Pages.PaymentsPage)(target)).KeyDown += new System.Windows.Input.KeyEventHandler(this.PaymentsPage_KeyDown);
            
            #line default
            #line hidden
            return;
            case 2:
            this.HeaderTotalPaymentsText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 3:
            this.HeaderTotalAmountText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 4:
            this.TotalPaymentsText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 5:
            this.TotalAmountText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 6:
            this.CashPaymentsText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 7:
            this.CardPaymentsText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 8:
            this.AddPaymentButton = ((System.Windows.Controls.Button)(target));
            
            #line 296 "..\..\..\..\Pages\PaymentsPage.xaml"
            this.AddPaymentButton.Click += new System.Windows.RoutedEventHandler(this.AddPaymentButton_Click);
            
            #line default
            #line hidden
            return;
            case 9:
            this.AddMultiPaymentButton = ((System.Windows.Controls.Button)(target));
            
            #line 308 "..\..\..\..\Pages\PaymentsPage.xaml"
            this.AddMultiPaymentButton.Click += new System.Windows.RoutedEventHandler(this.AddMultiPaymentButton_Click);
            
            #line default
            #line hidden
            return;
            case 10:
            this.RefreshButton = ((System.Windows.Controls.Button)(target));
            
            #line 322 "..\..\..\..\Pages\PaymentsPage.xaml"
            this.RefreshButton.Click += new System.Windows.RoutedEventHandler(this.RefreshButton_Click);
            
            #line default
            #line hidden
            return;
            case 11:
            this.ExportButton = ((System.Windows.Controls.Button)(target));
            
            #line 330 "..\..\..\..\Pages\PaymentsPage.xaml"
            this.ExportButton.Click += new System.Windows.RoutedEventHandler(this.ExportButton_Click);
            
            #line default
            #line hidden
            return;
            case 12:
            this.CompactViewToggle = ((System.Windows.Controls.Button)(target));
            
            #line 338 "..\..\..\..\Pages\PaymentsPage.xaml"
            this.CompactViewToggle.Click += new System.Windows.RoutedEventHandler(this.CompactViewToggle_Click);
            
            #line default
            #line hidden
            return;
            case 13:
            this.SearchTextBox = ((System.Windows.Controls.TextBox)(target));
            
            #line 361 "..\..\..\..\Pages\PaymentsPage.xaml"
            this.SearchTextBox.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.SearchTextBox_TextChanged);
            
            #line default
            #line hidden
            return;
            case 14:
            this.TodayFilterButton = ((System.Windows.Controls.Button)(target));
            
            #line 370 "..\..\..\..\Pages\PaymentsPage.xaml"
            this.TodayFilterButton.Click += new System.Windows.RoutedEventHandler(this.TodayFilter_Click);
            
            #line default
            #line hidden
            return;
            case 15:
            this.WeekFilterButton = ((System.Windows.Controls.Button)(target));
            
            #line 376 "..\..\..\..\Pages\PaymentsPage.xaml"
            this.WeekFilterButton.Click += new System.Windows.RoutedEventHandler(this.WeekFilter_Click);
            
            #line default
            #line hidden
            return;
            case 16:
            this.MonthFilterButton = ((System.Windows.Controls.Button)(target));
            
            #line 382 "..\..\..\..\Pages\PaymentsPage.xaml"
            this.MonthFilterButton.Click += new System.Windows.RoutedEventHandler(this.MonthFilter_Click);
            
            #line default
            #line hidden
            return;
            case 17:
            
            #line 390 "..\..\..\..\Pages\PaymentsPage.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ClearSearchButton_Click);
            
            #line default
            #line hidden
            return;
            case 18:
            this.ToggleFiltersButton = ((System.Windows.Controls.Button)(target));
            
            #line 398 "..\..\..\..\Pages\PaymentsPage.xaml"
            this.ToggleFiltersButton.Click += new System.Windows.RoutedEventHandler(this.ToggleFilters_Click);
            
            #line default
            #line hidden
            return;
            case 19:
            this.KeyboardShortcutsButton = ((System.Windows.Controls.Button)(target));
            
            #line 406 "..\..\..\..\Pages\PaymentsPage.xaml"
            this.KeyboardShortcutsButton.Click += new System.Windows.RoutedEventHandler(this.KeyboardShortcuts_Click);
            
            #line default
            #line hidden
            return;
            case 20:
            this.FiltersPanel = ((System.Windows.Controls.Border)(target));
            return;
            case 21:
            this.FromDatePicker = ((System.Windows.Controls.DatePicker)(target));
            
            #line 447 "..\..\..\..\Pages\PaymentsPage.xaml"
            this.FromDatePicker.SelectedDateChanged += new System.EventHandler<System.Windows.Controls.SelectionChangedEventArgs>(this.DateFilter_Changed);
            
            #line default
            #line hidden
            return;
            case 22:
            this.ToDatePicker = ((System.Windows.Controls.DatePicker)(target));
            
            #line 455 "..\..\..\..\Pages\PaymentsPage.xaml"
            this.ToDatePicker.SelectedDateChanged += new System.EventHandler<System.Windows.Controls.SelectionChangedEventArgs>(this.DateFilter_Changed);
            
            #line default
            #line hidden
            return;
            case 23:
            this.MinAmountTextBox = ((System.Windows.Controls.TextBox)(target));
            
            #line 464 "..\..\..\..\Pages\PaymentsPage.xaml"
            this.MinAmountTextBox.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.AmountFilter_Changed);
            
            #line default
            #line hidden
            return;
            case 24:
            this.MaxAmountTextBox = ((System.Windows.Controls.TextBox)(target));
            
            #line 472 "..\..\..\..\Pages\PaymentsPage.xaml"
            this.MaxAmountTextBox.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.AmountFilter_Changed);
            
            #line default
            #line hidden
            return;
            case 25:
            this.PaymentMethodComboBox = ((System.Windows.Controls.ComboBox)(target));
            
            #line 481 "..\..\..\..\Pages\PaymentsPage.xaml"
            this.PaymentMethodComboBox.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.PaymentMethodFilter_Changed);
            
            #line default
            #line hidden
            return;
            case 26:
            this.PaymentStatusComboBox = ((System.Windows.Controls.ComboBox)(target));
            
            #line 494 "..\..\..\..\Pages\PaymentsPage.xaml"
            this.PaymentStatusComboBox.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.PaymentStatusFilter_Changed);
            
            #line default
            #line hidden
            return;
            case 27:
            this.ApplyFiltersButton = ((System.Windows.Controls.Button)(target));
            
            #line 512 "..\..\..\..\Pages\PaymentsPage.xaml"
            this.ApplyFiltersButton.Click += new System.Windows.RoutedEventHandler(this.ApplyFilters_Click);
            
            #line default
            #line hidden
            return;
            case 28:
            this.ClearFiltersButton = ((System.Windows.Controls.Button)(target));
            
            #line 525 "..\..\..\..\Pages\PaymentsPage.xaml"
            this.ClearFiltersButton.Click += new System.Windows.RoutedEventHandler(this.ClearFilters_Click);
            
            #line default
            #line hidden
            return;
            case 29:
            this.PaymentCountText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 30:
            this.CompactModeToggle = ((System.Windows.Controls.Primitives.ToggleButton)(target));
            
            #line 562 "..\..\..\..\Pages\PaymentsPage.xaml"
            this.CompactModeToggle.Checked += new System.Windows.RoutedEventHandler(this.CompactMode_Changed);
            
            #line default
            #line hidden
            
            #line 563 "..\..\..\..\Pages\PaymentsPage.xaml"
            this.CompactModeToggle.Unchecked += new System.Windows.RoutedEventHandler(this.CompactMode_Changed);
            
            #line default
            #line hidden
            return;
            case 31:
            this.PaymentsDataGrid = ((System.Windows.Controls.DataGrid)(target));
            return;
            case 36:
            this.FooterPaymentCountText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 37:
            this.TotalDisplayedAmountText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 38:
            this.LastUpdateText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 39:
            this.LoadingPanel = ((System.Windows.Controls.Grid)(target));
            return;
            case 40:
            this.EmptyStatePanel = ((System.Windows.Controls.Grid)(target));
            return;
            case 41:
            
            #line 1015 "..\..\..\..\Pages\PaymentsPage.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.AddPaymentButton_Click);
            
            #line default
            #line hidden
            return;
            case 42:
            
            #line 1025 "..\..\..\..\Pages\PaymentsPage.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ClearSearchButton_Click);
            
            #line default
            #line hidden
            return;
            case 43:
            this.MultiPaymentOverlay = ((System.Windows.Controls.Grid)(target));
            return;
            case 44:
            this.MultiPaymentContainer = ((System.Windows.Controls.Grid)(target));
            return;
            case 45:
            this.PaymentDetailsOverlay = ((System.Windows.Controls.Grid)(target));
            return;
            case 46:
            this.PaymentDetailsContainer = ((System.Windows.Controls.Grid)(target));
            return;
            }
            this._contentLoaded = true;
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "*******")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        void System.Windows.Markup.IStyleConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 32:
            
            #line 809 "..\..\..\..\Pages\PaymentsPage.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ViewReceiptAttachment_Click);
            
            #line default
            #line hidden
            break;
            case 33:
            
            #line 834 "..\..\..\..\Pages\PaymentsPage.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ViewPaymentDetails_Click);
            
            #line default
            #line hidden
            break;
            case 34:
            
            #line 858 "..\..\..\..\Pages\PaymentsPage.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.EditPayment_Click);
            
            #line default
            #line hidden
            break;
            case 35:
            
            #line 880 "..\..\..\..\Pages\PaymentsPage.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.DeletePayment_Click);
            
            #line default
            #line hidden
            break;
            }
        }
    }
}

