using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using MedicalCenterWinForms.Data;
using MedicalCenterWinForms.Models;
using System.Data.SqlClient;
using System.Security.Cryptography;
using System.Text;

namespace MedicalCenterWinForms.Services
{
    public class DatabaseService
    {
        private readonly IConfiguration _configuration;

        public DatabaseService()
        {
            var builder = new ConfigurationBuilder()
                .SetBasePath(Directory.GetCurrentDirectory())
                .AddJsonFile("appsettings.json", optional: false, reloadOnChange: true);

            _configuration = builder.Build();
        }

        public ApplicationDbContext GetDbContext()
        {
            var connectionString = GetConnectionString();
            var options = new DbContextOptionsBuilder<ApplicationDbContext>()
                .UseSqlServer(connectionString)
                .Options;

            return new ApplicationDbContext(options);
        }

        public string GetConnectionString()
        {
            var useNetwork = bool.Parse(_configuration["AppSettings:UseNetworkDatabase"] ?? "false");
            
            if (useNetwork)
            {
                return _configuration.GetConnectionString("NetworkConnection") ?? 
                       throw new InvalidOperationException("Network connection string not found");
            }
            else
            {
                return _configuration.GetConnectionString("DefaultConnection") ?? 
                       throw new InvalidOperationException("Default connection string not found");
            }
        }

        public async Task<bool> TestConnectionAsync()
        {
            try
            {
                using var context = GetDbContext();
                await context.Database.CanConnectAsync();
                return true;
            }
            catch
            {
                return false;
            }
        }

        public async Task<bool> EnsureDatabaseCreatedAsync()
        {
            try
            {
                using var context = GetDbContext();
                await context.Database.EnsureCreatedAsync();
                return true;
            }
            catch
            {
                return false;
            }
        }

        public void UpdateNetworkSettings(string serverName, string databaseName, string username, string password)
        {
            var connectionString = $"Server={serverName};Database={databaseName};User Id={username};Password={password};MultipleActiveResultSets=true;TrustServerCertificate=true";

            // Update configuration in memory
            _configuration["ConnectionStrings:NetworkConnection"] = connectionString;
            _configuration["AppSettings:ServerName"] = serverName;
            _configuration["AppSettings:DatabaseName"] = databaseName;
            _configuration["AppSettings:Username"] = username;
            _configuration["AppSettings:Password"] = password;
        }

        public void SetUseNetworkDatabase(bool useNetwork)
        {
            _configuration["AppSettings:UseNetworkDatabase"] = useNetwork.ToString();
        }

        public bool IsUsingNetworkDatabase()
        {
            return bool.Parse(_configuration["AppSettings:UseNetworkDatabase"] ?? "false");
        }

        public (string serverName, string databaseName, string username, string password) GetNetworkSettings()
        {
            return (
                _configuration["AppSettings:ServerName"] ?? "",
                _configuration["AppSettings:DatabaseName"] ?? "",
                _configuration["AppSettings:Username"] ?? "",
                _configuration["AppSettings:Password"] ?? ""
            );
        }

        #region Authentication Methods

        /// <summary>
        /// Authenticate user with username and password
        /// </summary>
        public async Task<UserAccount?> AuthenticateUserAsync(string username, string password)
        {
            if (string.IsNullOrWhiteSpace(username) || string.IsNullOrWhiteSpace(password))
                return null;

            try
            {
                using var context = GetDbContext();

                // Find user by username
                var user = await context.UserAccounts
                    .FirstOrDefaultAsync(u => u.Username == username && u.IsActive);

                if (user == null)
                    return null;

                // Verify password
                if (VerifyPassword(password, user.HashedPassword))
                {
                    return user;
                }

                return null;
            }
            catch (Exception ex)
            {
                // Log error (in a real application, use proper logging)
                Console.WriteLine($"Authentication error: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// Update user information
        /// </summary>
        public async Task<bool> UpdateUserAsync(UserAccount user)
        {
            try
            {
                using var context = GetDbContext();
                context.UserAccounts.Update(user);
                await context.SaveChangesAsync();
                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Update user error: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Create default admin user if no users exist
        /// </summary>
        public async Task EnsureDefaultUsersAsync()
        {
            try
            {
                using var context = GetDbContext();

                // Check if any users exist
                if (await context.UserAccounts.AnyAsync())
                    return;

                // Create default users
                var defaultUsers = new[]
                {
                    new UserAccount
                    {
                        Username = "admin",
                        HashedPassword = HashPassword("admin123"),
                        Role = "Admin",
                        IsActive = true,
                        CreatedDate = DateTime.Now
                    },
                    new UserAccount
                    {
                        Username = "reception",
                        HashedPassword = HashPassword("reception123"),
                        Role = "Reception",
                        IsActive = true,
                        CreatedDate = DateTime.Now
                    },
                    new UserAccount
                    {
                        Username = "cashier",
                        HashedPassword = HashPassword("cashier123"),
                        Role = "Cashier",
                        IsActive = true,
                        CreatedDate = DateTime.Now
                    }
                };

                context.UserAccounts.AddRange(defaultUsers);
                await context.SaveChangesAsync();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error creating default users: {ex.Message}");
            }
        }

        /// <summary>
        /// Hash password using SHA256
        /// </summary>
        private string HashPassword(string password)
        {
            using var sha256 = SHA256.Create();
            var hashedBytes = sha256.ComputeHash(Encoding.UTF8.GetBytes(password + "MedicalCenter2025"));
            return Convert.ToBase64String(hashedBytes);
        }

        /// <summary>
        /// Verify password against hash
        /// </summary>
        private bool VerifyPassword(string password, string hash)
        {
            var passwordHash = HashPassword(password);
            return passwordHash == hash;
        }

        #endregion

    }
}
