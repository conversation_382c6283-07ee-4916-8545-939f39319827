using System.Drawing;

namespace MedicalCenterWinForms.Helpers
{
    public static class ArabicFontHelper
    {
        // Modern Arabic font families in order of preference for medical applications
        private static readonly string[] ArabicFonts = {
            "Tahoma",           // Best choice for Arabic text rendering
            "Arial Unicode MS", // Excellent Arabic support
            "Segoe UI",         // Modern Windows font with good Arabic support
            "Microsoft Sans Serif", // Good fallback
            "Cairo",            // Modern Arabic font (if available)
            "Noto Sans Arabic", // Google's modern Arabic font
            "Traditional Arabic" // System Arabic font
        };

        public static Font GetArabicFont(float size = 9F, FontStyle style = FontStyle.Regular)
        {
            foreach (var fontName in ArabicFonts)
            {
                try
                {
                    var font = new Font(fontName, size, style, GraphicsUnit.Point);
                    // Test if the font can display Arabic characters
                    if (CanDisplayArabic(font))
                    {
                        return font;
                    }
                }
                catch
                {
                    // Continue to next font if this one fails
                    continue;
                }
            }

            // Ultimate fallback - use Tahoma which has best Arabic support
            return new Font("Tahoma", size, style, GraphicsUnit.Point);
        }

        /// <summary>
        /// Process Arabic text for proper display in Windows Forms
        /// </summary>
        /// <param name="text">Arabic text to process</param>
        /// <returns>Processed text for proper display</returns>
        public static string ProcessArabicText(string text)
        {
            if (string.IsNullOrEmpty(text))
                return text;

            // Ensure proper Arabic text rendering
            // This helps with text shaping and BiDi support
            return text.Trim();
        }

        // Modern font methods with enhanced sizing for medical applications
        public static Font GetArabicHeaderFont(float size = 14F, FontStyle style = FontStyle.Bold)
        {
            return GetArabicFont(size, style);
        }

        public static Font GetArabicTitleFont(float size = 18F, FontStyle style = FontStyle.Bold)
        {
            return GetArabicFont(size, style);
        }

        public static Font GetArabicButtonFont(float size = 12F, FontStyle style = FontStyle.Bold)
        {
            return GetArabicFont(size, style);
        }

        public static Font GetArabicLabelFont(float size = 11F, FontStyle style = FontStyle.Regular)
        {
            return GetArabicFont(size, style);
        }

        public static Font GetArabicTextBoxFont(float size = 12F, FontStyle style = FontStyle.Regular)
        {
            return GetArabicFont(size, style);
        }

        // New modern font methods for medical UI
        public static Font GetArabicSubtitleFont(float size = 16F)
        {
            return GetArabicFont(size, FontStyle.Regular);
        }

        public static Font GetArabicCaptionFont(float size = 10F)
        {
            return GetArabicFont(size, FontStyle.Regular);
        }

        public static Font GetArabicDisplayFont(float size = 24F)
        {
            return GetArabicFont(size, FontStyle.Bold);
        }

        public static Font GetArabicBodyFont(float size = 13F)
        {
            return GetArabicFont(size, FontStyle.Regular);
        }

        private static bool CanDisplayArabic(Font font)
        {
            try
            {
                // Test with a simple Arabic character
                using var bitmap = new Bitmap(1, 1);
                using var graphics = Graphics.FromImage(bitmap);
                var testString = "ا"; // Arabic letter Alef
                var size = graphics.MeasureString(testString, font);
                return size.Width > 0;
            }
            catch
            {
                return false;
            }
        }

        public static void ApplyArabicFont(Control control, float? fontSize = null, FontStyle? fontStyle = null)
        {
            if (control == null) return;

            var size = fontSize ?? control.Font?.Size ?? 9F;
            var style = fontStyle ?? control.Font?.Style ?? FontStyle.Regular;

            control.Font = GetArabicFont(size, style);

            // Apply to all child controls recursively
            foreach (Control child in control.Controls)
            {
                ApplyArabicFont(child, fontSize, fontStyle);
            }
        }

        public static void SetupArabicForm(Form form)
        {
            if (form == null) return;

            // Set form properties for Arabic
            form.RightToLeft = RightToLeft.Yes;
            form.RightToLeftLayout = true;

            // Apply Arabic font to the form and all its controls
            ApplyArabicFont(form);

            // Set specific fonts for different control types
            SetControlFonts(form);
        }

        private static void SetControlFonts(Control parent)
        {
            foreach (Control control in parent.Controls)
            {
                switch (control)
                {
                    case Button button:
                        button.Font = GetArabicButtonFont(10F);
                        break;
                    case Label label when label.Font.Style.HasFlag(FontStyle.Bold):
                        label.Font = GetArabicHeaderFont(label.Font.Size);
                        break;
                    case Label label:
                        label.Font = GetArabicLabelFont(label.Font.Size);
                        break;
                    case TextBox textBox:
                        textBox.Font = GetArabicTextBoxFont(textBox.Font.Size);
                        textBox.TextAlign = HorizontalAlignment.Right;
                        break;
                    case ComboBox comboBox:
                        comboBox.Font = GetArabicTextBoxFont(comboBox.Font.Size);
                        comboBox.RightToLeft = RightToLeft.Yes;
                        break;
                    case DataGridView dataGridView:
                        dataGridView.Font = GetArabicFont(9F);
                        dataGridView.RightToLeft = RightToLeft.Yes;
                        dataGridView.DefaultCellStyle.Font = GetArabicFont(9F);
                        dataGridView.ColumnHeadersDefaultCellStyle.Font = GetArabicHeaderFont(10F);
                        break;
                    case MenuStrip menuStrip:
                        menuStrip.Font = GetArabicFont(9F);
                        menuStrip.RightToLeft = RightToLeft.Yes;
                        break;
                    case StatusStrip statusStrip:
                        statusStrip.Font = GetArabicFont(8F);
                        statusStrip.RightToLeft = RightToLeft.Yes;
                        break;
                }

                // Recursively apply to child controls
                if (control.HasChildren)
                {
                    SetControlFonts(control);
                }
            }
        }

        public static void SetupArabicDataGridView(DataGridView dgv)
        {
            if (dgv == null) return;

            dgv.RightToLeft = RightToLeft.Yes;
            dgv.Font = GetArabicFont(9F);
            dgv.DefaultCellStyle.Font = GetArabicFont(9F);
            dgv.ColumnHeadersDefaultCellStyle.Font = GetArabicHeaderFont(10F);
            dgv.DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleRight;
            dgv.ColumnHeadersDefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;

            // Set RTL for all columns
            foreach (DataGridViewColumn column in dgv.Columns)
            {
                column.DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleRight;
                column.HeaderCell.Style.Alignment = DataGridViewContentAlignment.MiddleCenter;
            }
        }
    }
}
